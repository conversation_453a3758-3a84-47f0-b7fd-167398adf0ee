/*!
 * @file
 * @brief This file defines public constants, types and functions for the system manager.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef SYSTEMMANAGER_H
#define SYSTEMMANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "SimpleFsm.h"

#define DebugMode (0)
#define ReleaseMode (1)

#define BuildMode ReleaseMode
//#define BuildMode DebugMode

#if(BuildMode == DebugMode)

#define STATIC

#else

#define STATIC static

#endif

#define U16_FRIDGESTARTUP_DELAY_SECOND (uint16_t)5

enum
{
    eFridge_Startup = 0,
    eFridge_Running,
    eFridge_FunctionalCircuitTest,
    eFridge_Factory,
    eFridge_Showroom,
    eFridge_Test
};
typedef uint8_t FridgeState_t;

void SystemManager_Init(void);
void FridgeState_Update(FridgeState_t requestState);
void Set_FactoryEntryNumber(uint8_t entryNumber);
uint8_t Get_FactoryEntryNumber(void);
FridgeState_t Get_FridgeState(void);

#endif
