/*
 * Iot_Spec.c
 *
 *  Created on: 2023年7月3日
 *      Author: Mi
 */

#include <string.h>
#include "arch_os.h"
#include "Iot_Spec.h"


IotFridgeHouse_t gIotFridge;
TLong gActionParams[ACTION_INPRAM_PROP_MAX_LENGTH];


void initIotFridgeHouse(void)
{
	memset(&gIotFridge, 0, sizeof(gIotFridge));
    gIotFridge.iot_down.foodAlarmDays = 0x8000;
    gIotFridge.dev_down.peekValleyPowerRefSet = 5;
    gIotFridge.dev_down.peekValleyPowerFrzSet = -18;

//	set_dev_wash_param_by_name(global, detergent_material, IOT_MR_NORMAL);
//	set_dev_wash_param_by_name(global, fabric_material, IOT_MR_NORMAL);
//	set_dev_wash_param_by_name(global, material_clean_slot, IOT_SLOT_DETERGENT);
//	set_dev_wash_param_by_name(down, mode, IOT_M_MIX);
//	set_dev_wash_param_by_name(down, reserv_status, IOT_REV_START_WASH);

//	//TODO: read factory deadline date from flash
//	set_dev_wash_param_by_name(global, factory_deadline, DEFAULT_FACTORY_REPORT_DEADLINE);
}


static const IotPropCheckValue_t sIotPropValueCheckTable[IOT_PROP_MAX] =
{
	//IOT_PROP_FAULT
	{FALSE, 0, 0x7FFFFFFF, 1},

	//IOT_PROP_MODE
	{TRUE, 0, 10, 0},
		
	//IOT_PROP_DOOR_ALARM
	{FALSE, 0, 255, 0},

	//IOT_PROP_REF_TEMP
	{FALSE, -50, 100, 1},

	//IOT_PROP_REF_SET
	{TRUE, 2, 8, 1},

	//IOT_PROP_REF_OFF
	{TRUE, 0, 1, 1},

    //IOT_PROP_REF_IONSTER
	{TRUE, 0, 1, 1},

	//IOT_PROP_FRZ_TEMP
	{FALSE, -50, 100, 1},

	//IOT_PROP_FRZ_SET
	{TRUE, -24, -16, 1},

	//IOT_PROP_SUPER_FRZ_SET
	{TRUE, -30, -16, 1},

	//IOT_PROP_DEEPSUPER_FRZ_SET
	{TRUE, -30, -16, 1},

    //IOT_PROP_FRZ_ICONSTER
	{TRUE, 0, 1, 1},

	//IOT_PROP_REFVAR_TEMP
	{FALSE, -50, 100, 1},

	//IOT_PROP_REFVAR_SET
	{TRUE, -1, 5, 1},      

	//IOT_PROP_REFVAR_MODE
	{TRUE, 0, 10, 1},

	//IOT_PROP_REFVAR_BOTTOMTEMP
	{FALSE, -50, 100, 1},

	//IOT_PROP_REFVAR_TOPTEMP
	{FALSE, -50, 100, 1},
    	
    //IOT_PROP_REFVAR_BOTTOMBACKTEMP
	{FALSE, -50, 100, 1},

	//IOT_PROP_ICEMAKER_MODE
	{TRUE, 0, 3, 1},

	//IOT_PROP_ICEMAKER_STATE
	{TRUE, 0, 3, 1},

    //IOT_PROP_ICEMAKER_RESERVETIME
	{TRUE, 0, 0x7FFFFFFF, 1},

    //IOT_PROP_ICEMAKER_SIZE
	{TRUE, 0, 1, 1},

    //IOT_PROP_ICEMAKER_RESERVE
	{TRUE, 0, 1, 1},

	//IOT_DL_PROP_DEFROST_STATUS
	{FALSE, 0, 1, 1},

	//IOT_DL_PROP_DEFROST_TEMP
	{FALSE, -50, 100, 1},

	//IOT_DL_PROP_REF_DAMPER
	{FALSE, 0, 1, 1},

	//IOT_DL_PROP_FRZ_FAN
	{FALSE, 0, 255, 1},    

	//IOT_DL_PROP_COMP_SPEED
	{FALSE, 0, 10000, 1},    

	//IOT_DL_PROP_COOL_FAN
	{FALSE, 0, 255, 1},    

	//IOT_DL_PROP_VAR_DAMPER
	{FALSE, 0, 1, 1},    

	//IOT_DL_PROP_REFVAR_DAMPER
	{FALSE, 0, 1, 1},

	//IOT_DL_PROP_ROOMTTEMP
	{FALSE, -50, 100, 1},

	//IOT_DL_PROP_HUMIDITY
	{FALSE, 0, 100, 1},
    
    //IOT_PROP_DOOR_STATE
	{FALSE, 0, 65535, 1},
	
    //IOT_PROP_PEEK_VALLEY_POWER
	{TRUE, 0, 1, 1},
    //IOT_PROP_LINGYUN_POWER
    {TRUE, 0, 1, 1},
    //IOT_PROP_PEEK_VALLEY_REF_SET
    {TRUE, 2, 8, 1},
    //IOT_PROP_PEEK_VALLEY_FRZ_SET
    {TRUE, -24, -16, 1},
    //IOT_PROP_PEEK_VALLEY_DEFORST
    {TRUE, 0, 1, 1},
    //IOT_PROP_FOOD_ALARM
    {FALSE, 0, 2, 1},
    //IOT_PROP_FOOD_UNALARM
    {FALSE, 0, 2, 1},
    //IOT_PROP_FOOD_ALARM_ROOM
    {TRUE, 0, 10, 1},
    //IOT_PROP_FOOD_ALARM_DAYS
    {TRUE, -32768, 32767, 1},
    //IOT_PROP_LINYUN_POWER_PARAM
    {TRUE, -32768, 32767, 1},
    //IOT_PROP_STRONG_COOL
    {TRUE, 0, 1, 1},
    //IOT_PROP_STRONG_MUTE
    {TRUE, 0, 1, 1},
    //IOT_PROP_LINGYUN_MUTE
    {TRUE, 0, 1, 1},
    //IOT_PROP_MUTE_MODE
    {TRUE, 0, 2, 1},
    //IOT_PROP_FACTORY_SN
    {TRUE, 0, 65535, 1},

    //IOT_PROP_FACTORY_DATA
    {FALSE, 0, 65535, 1},
    
    //IOT_PROP_MAINTENANCE_SECTION1
    {TRUE, 0, 65535, 1},
    //IOT_PROP_MAINTENANCE_SECTION2
    {TRUE, 0, 65535, 1},
    //IOT_PROP_MAINTENANCE_SECTION3
    {TRUE, 0, 65535, 1},
    //IOT_PROP_MAINTENANCE_SECTION4
    {TRUE, 0, 65535, 1},
    //IOT_PROP_MAINTENANCE_SECTION5
    {TRUE, 0, 65535, 1},
    //IOT_PROP_MAINTENANCE_SECTION6
    {TRUE, 0, 65535, 1},
    //IOT_PROP_MAINTENANCE_SECTION7
    {TRUE, 0, 65535, 1},
    //IOT_PROP_MAINTENANCE_SECTION8
    {TRUE, 0, 65535, 1},
    //IOT_PROP_INVERTERFAULT,          //10.12 
    {FALSE, 0, 255, 1},

    //IOT_PROP_SYSTEMSTATUS,           //10.13 
    {FALSE, 0, 255, 1},

    //IOT_PROP_DEFROSTREASON,          //10.14 
    {FALSE, 0, 255, 1},

    //IOT_PROP_DEFROSTSTEP,            //10.15 
    {FALSE, 0, 255, 1},

    //IOT_PROP_COMPRESSORSTATUS,       //10.16 
    {FALSE, 0, 255, 1},

    //IOT_PROP_FREEZERSTATUS,          //10.17 
    {FALSE, 0, 255, 1},

    //IOT_PROP_ROOMFREEZERSTATE,       //10.18 
    {FALSE, 0, 255, 1},

    //IOT_PROP_REFFANLEVEL,            //10.19     
    {FALSE, 0, 255, 1},

    //IOT_PROP_COMPFEEDBACKFREQ,       //10.20 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_COMPFEEDBACKPOWER,      //10.21 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_COMPFEEDBACKVOL,        //10.22 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_TOTALRUNTIME,           //10.23 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_COMPTOTALTIME,          //10.24 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_COMRUNTIME,             //10.25 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_DEFROSTPRETIME,         //10.26 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_FRZHEATERONTIME,        //10.27 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_SUPERMODETIME,          //10.28 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_DEFROSTRUNTIME,         //10.29 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_HEATERSTATUS,           //10.30 
    {FALSE, 0, 255, 1},

    //IOT_PROP_ICELOADSTATUS,          //10.31 
    {FALSE, 0, 255, 1},

    //IOT_PROP_ICEMAKINGSTATUS,        //10.32 
    {FALSE, 0, 255, 1},

    //IOT_PROP_ICETARGETTIME,          //10.33 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_ICERUNTIME,             //10.34 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_ICECURRENTTIME,         //10.35 
    {FALSE, 0, 65535, 1},

    //IOT_PROP_ERRORQUERY,             //10.36 
    {FALSE, 0, 0x7FFFFFFF, 1},
    
    //IOT_PROP_VALVE_STATUS,           //10.37 
    {FALSE, 0, 255, 1},
    
    //IOT_PROP_REF_DOOR_TIMES,         //10.38 
    {FALSE, 0, 255, 1},
    
    //IOT_PROP_REF_DOOR_TIMER,         //10.39 
    {FALSE, 0, 65535, 1},
    
    //IOT_PROP_FRZ_DOOR_TIMES,         //10.40 
    {FALSE, 0, 255, 1},
    
    //IOT_PROP_FRZ_DOOR_TIMER,         //10.41 
    {FALSE, 0, 65535, 1},
        
    //IOT_PROP_REF_DEF_SNR,            //10.43 
    {FALSE, -50, 100, 1},
};

//static const IotPropList_t temperature_list[] = {
//	{0, 0},
//	{40, 40},
//	{60, 60},
//	{95, 95},
//};

static const IotPropListTable_e propListTable[] = {
//	{IOT_PROP_TEMPERATURE, sizeof(temperature_list)/sizeof(IotPropList_t), (IotPropList_t*)&temperature_list},
	//{IOT_PROP_SPIN_SPEED, sizeof(spin_speed_list)/sizeof(IotPropList_t), (IotPropList_t*)&spin_speed_list},
};

static IotPropState_t sIotPropStateTable[IOT_PROP_MAX] =
{
	//Dev Info

	//IOT_PROP_FAULT
	{
		&gIotFridge.dev_down.error1, &gIotFridge.iot_down.error1, 
		IOT_TYPE_WORD, 2, 1, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
	//IOT_PROP_MODE
	{
		&gIotFridge.dev_down.mode, &gIotFridge.iot_down.mode, 
		IOT_TYPE_BYTE, 2, 2, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
	//IOT_PROP_DOOR_ALARM
	{
		&gIotFridge.dev_down.doorAlarm, &gIotFridge.iot_down.doorAlarm, 
		IOT_TYPE_BYTE, 2, 3, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
	//IOT_PROP_REF_TEMP
	{
		&gIotFridge.dev_down.refTemp, &gIotFridge.iot_down.refTemp, 
		IOT_TYPE_BYTE, 3, 1, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
	//IOT_PROP_REF_SET
	{
		&gIotFridge.dev_down.refSet, &gIotFridge.iot_down.refSet, 
		IOT_TYPE_BYTE, 3, 2, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
	//IOT_PROP_REF_OFF
	{
		&gIotFridge.dev_down.refOff, &gIotFridge.iot_down.refOff, 
		IOT_TYPE_BOOL, 3, 3, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_REF_IONSTER
	{
		&gIotFridge.dev_down.refIonSter, &gIotFridge.iot_down.refIonSter, 
		IOT_TYPE_BOOL, 3, 4, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_FRZ_TEMP
	{
		&gIotFridge.dev_down.frzTemp, &gIotFridge.iot_down.frzTemp, 
		IOT_TYPE_BYTE, 4, 1, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
	//IOT_PROP_FRZ_SET
	{
		&gIotFridge.dev_down.frzSet, &gIotFridge.iot_down.frzSet, 
		IOT_TYPE_BYTE, 4, 2, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_SUPER_FRZ_SET
	{
		&gIotFridge.dev_down.superfrzSet, &gIotFridge.iot_down.superfrzSet, 
		IOT_TYPE_BYTE, 4, 3, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_DEEPSUPER_FRZ_SET
	{
		&gIotFridge.dev_down.deepSuperfrzSet, &gIotFridge.iot_down.deepSuperfrzSet, 
		IOT_TYPE_BYTE, 4, 4, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_FRZ_ICONSTER
	{
		&gIotFridge.dev_down.frzIonSter, &gIotFridge.iot_down.frzIonSter, 
		IOT_TYPE_BOOL, 4, 5, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
	//IOT_PROP_REFVAR_TEMP
	{
		&gIotFridge.dev_down.refvarTemp, &gIotFridge.iot_down.refvarTemp, 
		IOT_TYPE_BYTE, 6, 1, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
	//IOT_PROP_REFVAR_SET
	{
		&gIotFridge.dev_down.refvarSet, &gIotFridge.iot_down.refvarSet, 
		IOT_TYPE_BYTE, 6, 2, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},                            
	//IOT_PROP_REFVAR_MODE
	{
		&gIotFridge.dev_down.refvarMode, &gIotFridge.iot_down.refvarMode, 
		IOT_TYPE_BYTE, 6, 3, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_REFVAR_BOTTOMTEMP
	{
		&gIotFridge.dev_down.refvarTempBottom, &gIotFridge.iot_down.refvarTempBottom, 
		IOT_TYPE_BYTE, 6, 4, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_REFVAR_TOPTEMP
	{
		&gIotFridge.dev_down.refvarTempTop, &gIotFridge.iot_down.refvarTempTop, 
		IOT_TYPE_BYTE, 6, 5, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_REFVAR_BOTTOMBACKTEMP
	{
		&gIotFridge.dev_down.refvarTempBottomBack, &gIotFridge.iot_down.refvarTempBottomBack, 
		IOT_TYPE_BYTE, 6, 6, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_ICEMAKER_MODE
	{
		&gIotFridge.dev_down.icemakerMode, &gIotFridge.iot_down.icemakerMode, 
		IOT_TYPE_BYTE, 8, 1, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_ICEMAKER_STATE
	{
		&gIotFridge.dev_down.icemakerState, &gIotFridge.iot_down.icemakerState, 
		IOT_TYPE_BYTE, 8, 3, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_ICEMAKER_RESERVETIME
	{
		&gIotFridge.dev_down.icemakerReserveTime, &gIotFridge.iot_down.icemakerReserveTime, 
		IOT_TYPE_WORD, 8, 4, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_ICEMAKER_SIZE
	{
		&gIotFridge.dev_down.icemakerSize, &gIotFridge.iot_down.icemakerSize, 
		IOT_TYPE_BYTE, 8, 5, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_PROP_ICEMAKER_RESERVE
	{
		&gIotFridge.dev_down.iceMakerReserve, &gIotFridge.iot_down.iceMakerReserve, 
		IOT_TYPE_BOOL, 8, 6, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_WHEN_APP_CONNECT
	},
    //IOT_DL_PROP_DEFROST_STATUS
	{
		&gIotFridge.dev_down.defroststatus, &gIotFridge.iot_down.defroststatus, 
		IOT_TYPE_BYTE, 10, 1, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
    //IOT_DL_PROP_DEFROST_TEMP
	{
		&gIotFridge.dev_down.defrosttemp, &gIotFridge.iot_down.defrosttemp, 
		IOT_TYPE_BYTE, 10, 2, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

	//IOT_DL_PROP_REF_DAMPER
	{
		&gIotFridge.dev_down.refdamper, &gIotFridge.iot_down.refdamper, 
		IOT_TYPE_BYTE, 10, 3, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

	//IOT_DL_PROP_FRZ_FAN
	{
		&gIotFridge.dev_down.frzfanlevel, &gIotFridge.iot_down.frzfanlevel, 
		IOT_TYPE_BYTE, 10, 4, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},   

	//IOT_DL_PROP_COMP_SPEED
	{
		&gIotFridge.dev_down.compspeed, &gIotFridge.iot_down.compspeed, 
		IOT_TYPE_SHORT, 10, 5, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},   

	//IOT_DL_PROP_COOL_FAN
	{
		&gIotFridge.dev_down.coolfanlevel, &gIotFridge.iot_down.coolfanlevel, 
		IOT_TYPE_BYTE, 10, 6, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},   

	//IOT_DL_PROP_VAR_DAMPER
	{
		&gIotFridge.dev_down.vardamper, &gIotFridge.iot_down.vardamper, 
		IOT_TYPE_BYTE, 10, 7, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},  

	//IOT_DL_PROP_REFVAR_DAMPER
	{
		&gIotFridge.dev_down.refvardamper, &gIotFridge.iot_down.refvardamper, 
		IOT_TYPE_BYTE, 10, 8, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

	//IOT_DL_PROP_ROOMTTEMP
	{
		&gIotFridge.dev_down.roomtemp, &gIotFridge.iot_down.roomtemp, 
		IOT_TYPE_BYTE, 10, 9, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

	//IOT_DL_PROP_HUMIDITY
	{
		&gIotFridge.dev_down.humidity, &gIotFridge.iot_down.humidity, 
		IOT_TYPE_BYTE, 10, 10, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
    
    //IOT_PROP_DOOR_STATE
	{
		&gIotFridge.dev_down.doorState, &gIotFridge.iot_down.doorState, 
		IOT_TYPE_BYTE, 10, 11, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

    //IOT_PROP_PEEK_VALLEY_POWER
    {
		&gIotFridge.dev_down.peekValleyPower, &gIotFridge.iot_down.peekValleyPower, 
		IOT_TYPE_BOOL, 2, 5, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
    //IOT_PROP_LINGYUN_POWER
    {
		&gIotFridge.dev_down.lingyunPower, &gIotFridge.iot_down.lingyunPower, 
		IOT_TYPE_BOOL, 2, 6, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
    //IOT_PROP_PEEK_VALLEY_REF_SET
    {
		&gIotFridge.dev_down.peekValleyPowerRefSet, &gIotFridge.iot_down.peekValleyPowerRefSet, 
		IOT_TYPE_BYTE, 13, 1, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
    //IOT_PROP_PEEK_VALLEY_FRZ_SET
    {
		&gIotFridge.dev_down.peekValleyPowerFrzSet, &gIotFridge.iot_down.peekValleyPowerFrzSet, 
		IOT_TYPE_BYTE, 13, 2, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
    //IOT_PROP_PEEK_VALLEY_DEFORST
    {
		&gIotFridge.dev_down.peekValleyDeforst, &gIotFridge.iot_down.peekValleyDeforst, 
		IOT_TYPE_BOOL, 13, 3, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
	//IOT_PROP_FOOD_ALARM
    {
		&gIotFridge.dev_down.foodAlarm, &gIotFridge.iot_down.foodAlarm, 
		IOT_TYPE_BYTE, 9, 1, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
    //IOT_PROP_FOOD_UNALARM
    {
		&gIotFridge.dev_down.foodUnalarm, &gIotFridge.iot_down.foodUnalarm, 
		IOT_TYPE_BYTE, 13, 4, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},
    //IOT_PROP_FOOD_ALARM_ROOM
    {
		&gIotFridge.dev_down.foodAlarmRoom, &gIotFridge.iot_down.foodAlarmRoom, 
		IOT_TYPE_SHORT, 13, 5, 
		0, 
		0
	},
    //IOT_PROP_FOOD_ALARM_DAYS
    {
		&gIotFridge.dev_down.foodAlarmDays, &gIotFridge.iot_down.foodAlarmDays, 
		IOT_TYPE_SHORT, 13, 6, 
		0, 
		0
	},

    //IOT_PROP_LINYUN_POWER_PARAM
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 13, 8, 
		0, 
		0
	},

    //IOT_PROP_STRONG_COOL
    {
		&gIotFridge.dev_down.strongCool, &gIotFridge.iot_down.strongCool, 
		IOT_TYPE_BOOL, 2, 9, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

    //IOT_PROP_STRONG_MUTE
    {
		&gIotFridge.dev_down.strongMute, &gIotFridge.iot_down.strongMute, 
		IOT_TYPE_BOOL, 2, 8, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

    //IOT_PROP_LINGYUN_MUTE
    {
		&gIotFridge.dev_down.lingyunMute, &gIotFridge.iot_down.lingyunMute, 
		IOT_TYPE_BOOL, 2, 7, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

    //IOT_PROP_MUTE_MODE
    {
		&gIotFridge.dev_down.muteMode, &gIotFridge.iot_down.muteMode, 
		IOT_TYPE_BYTE, 13, 9, 
		PROP_POLICY_BASIC_INFO, 
		PROP_FLAG_UPDATED_BY_DEV
	},

    //IOT_PROP_FACTORY_SN
    {
		&gIotFridge.dev_down.sn, &gIotFridge.iot_down.sn, 
		IOT_TYPE_STRING, 1, 5, 
		0, 
		0
	},
    //IOT_PROP_FACTORY_DATA
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 254, 1, 
		0, 
		0
	},

	//IOT_PROP_MAINTENANCE_SECTION1
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 14, 1, 
		0, 
		0
	},
	//IOT_PROP_MAINTENANCE_SECTION2
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 14, 2, 
		0, 
		0
	},
	//IOT_PROP_MAINTENANCE_SECTION3
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 14, 3, 
		0, 
		0
	},
	//IOT_PROP_MAINTENANCE_SECTION4
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 14, 4, 
		0, 
		0
	},
	//IOT_PROP_MAINTENANCE_SECTION5
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 14, 5, 
		0, 
		0
	},
	//IOT_PROP_MAINTENANCE_SECTION6
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 14, 6, 
		0, 
		0
	},
	//IOT_PROP_MAINTENANCE_SECTION7
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 14, 7, 
		0, 
		0
	},
	//IOT_PROP_MAINTENANCE_SECTION8
    {
		NULL, NULL, 
		IOT_TYPE_STRING, 14, 8, 
		0, 
		0
	},
	
    //IOT_PROP_INVERTERFAULT,          //10.12 
    {
        &gIotFridge.dev_down.inverterFault, &gIotFridge.iot_down.inverterFault, 
        IOT_TYPE_BYTE, 10, 12, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_SYSTEMSTATUS,           //10.13 
    {
        &gIotFridge.dev_down.systemStatus, &gIotFridge.iot_down.systemStatus, 
        IOT_TYPE_BYTE, 10, 13, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_DEFROSTREASON,          //10.14 
    {
        &gIotFridge.dev_down.defrostReason, &gIotFridge.iot_down.defrostReason, 
        IOT_TYPE_BYTE, 10, 14, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_DEFROSTSTEP,            //10.15 
    {
        &gIotFridge.dev_down.defrostStep, &gIotFridge.iot_down.defrostStep, 
        IOT_TYPE_BYTE, 10, 15, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_COMPRESSORSTATUS,       //10.16 
    {
        &gIotFridge.dev_down.compressorStatus, &gIotFridge.iot_down.compressorStatus, 
        IOT_TYPE_BYTE, 10, 16, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_FREEZERSTATUS,          //10.17 
    {
        &gIotFridge.dev_down.freezerStatus, &gIotFridge.iot_down.freezerStatus, 
        IOT_TYPE_BYTE, 10, 17, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_ROOMFREEZERSTATE,       //10.18 
    {
        &gIotFridge.dev_down.roomFreezerState, &gIotFridge.iot_down.roomFreezerState, 
        IOT_TYPE_BYTE, 10, 18, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_REFFANLEVEL,            //10.19     
    {
        &gIotFridge.dev_down.refFanLevel, &gIotFridge.iot_down.refFanLevel, 
        IOT_TYPE_BYTE, 10, 19, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_COMPFEEDBACKFREQ,       //10.20 
    {
        &gIotFridge.dev_down.compFeedbackFreq, &gIotFridge.iot_down.compFeedbackFreq, 
        IOT_TYPE_SHORT, 10, 20, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_COMPFEEDBACKPOWER,      //10.21 
    {
        &gIotFridge.dev_down.compFeedbackPower, &gIotFridge.iot_down.compFeedbackPower, 
        IOT_TYPE_SHORT, 10, 21, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_COMPFEEDBACKVOL,        //10.22 
    {
        &gIotFridge.dev_down.compFeedbackVol, &gIotFridge.iot_down.compFeedbackVol, 
        IOT_TYPE_SHORT, 10, 22, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_TOTALRUNTIME,           //10.23 
    {
        &gIotFridge.dev_down.totalRuntime, &gIotFridge.iot_down.totalRuntime, 
        IOT_TYPE_SHORT, 10, 23, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_COMPTOTALTIME,          //10.24 
    {
        &gIotFridge.dev_down.compTotalTime, &gIotFridge.iot_down.compTotalTime, 
        IOT_TYPE_SHORT, 10, 24, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_COMRUNTIME,             //10.25 
    {
        &gIotFridge.dev_down.comRuntime, &gIotFridge.iot_down.comRuntime, 
        IOT_TYPE_SHORT, 10, 25, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_DEFROSTPRETIME,         //10.26 
    {
        &gIotFridge.dev_down.defrostPreTime, &gIotFridge.iot_down.defrostPreTime, 
        IOT_TYPE_SHORT, 10, 26, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_FRZHEATERONTIME,        //10.27 
    {
        &gIotFridge.dev_down.frzHeaterOnTime, &gIotFridge.iot_down.frzHeaterOnTime, 
        IOT_TYPE_SHORT, 10, 27, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_SUPERMODETIME,          //10.28 
    {
        &gIotFridge.dev_down.supermodeTime, &gIotFridge.iot_down.supermodeTime, 
        IOT_TYPE_SHORT, 10, 28, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_DEFROSTRUNTIME,         //10.29 
    {
        &gIotFridge.dev_down.defrostRuntime, &gIotFridge.iot_down.defrostRuntime, 
        IOT_TYPE_SHORT, 10, 29, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_HEATERSTATUS,           //10.30 
    {
        &gIotFridge.dev_down.heaterStatus, &gIotFridge.iot_down.heaterStatus, 
        IOT_TYPE_BYTE, 10, 30, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_ICELOADSTATUS,          //10.31 
    {
        &gIotFridge.dev_down.iceLoadStatus, &gIotFridge.iot_down.iceLoadStatus, 
        IOT_TYPE_BYTE, 10, 31, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_ICEMAKINGSTATUS,        //10.32 
    {
        &gIotFridge.dev_down.iceMakingStatus, &gIotFridge.iot_down.iceMakingStatus, 
        IOT_TYPE_BYTE, 10, 32, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_ICETARGETTIME,          //10.33 
    {
        &gIotFridge.dev_down.iceTargetTime, &gIotFridge.iot_down.iceTargetTime, 
        IOT_TYPE_SHORT, 10, 33, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_ICERUNTIME,             //10.34 
    {
        &gIotFridge.dev_down.iceRunTime, &gIotFridge.iot_down.iceRunTime, 
        IOT_TYPE_SHORT, 10, 34, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_ICECURRENTTIME,         //10.35 
    {
        &gIotFridge.dev_down.iceCurrentTime, &gIotFridge.iot_down.iceCurrentTime, 
        IOT_TYPE_SHORT, 10, 35, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_ERRORQUERY,             //10.36 
    {
        &gIotFridge.dev_down.errorQuery, &gIotFridge.iot_down.errorQuery, 
        IOT_TYPE_WORD, 10, 36, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },
    
    //IOT_PROP_VALVE_STATUS,           //10.37 
    {
        &gIotFridge.dev_down.valveStatus, &gIotFridge.iot_down.valveStatus, 
        IOT_TYPE_BYTE, 10, 37, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_REF_DOOR_TIMES,         //10.38 
    {
        &gIotFridge.dev_down.refDoorTimes, &gIotFridge.iot_down.refDoorTimes, 
        IOT_TYPE_BYTE, 10, 38, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_REF_DOOR_TIMER,         //10.39 
    {
        &gIotFridge.dev_down.refDoorTimer, &gIotFridge.iot_down.refDoorTimer, 
        IOT_TYPE_SHORT, 10, 39, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_FRZ_DOOR_TIMES,         //10.40 
    {
        &gIotFridge.dev_down.frzDoorTimes, &gIotFridge.iot_down.frzDoorTimes, 
        IOT_TYPE_BYTE, 10, 40, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },

    //IOT_PROP_FRZ_DOOR_TIMER,         //10.41 
    {
        &gIotFridge.dev_down.frzDoorTimer, &gIotFridge.iot_down.frzDoorTimer, 
        IOT_TYPE_SHORT, 10, 41, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },            
         
    //IOT_PROP_REF_DEF_SNR,         //10.43 
    {
        &gIotFridge.dev_down.refDefSnr, &gIotFridge.iot_down.refDefSnr, 
        IOT_TYPE_BYTE, 10, 43, 
        PROP_POLICY_BASIC_INFO, 
        PROP_FLAG_UPDATED_BY_DEV
    },    
};

#define PROPLIST_TABLE_LENTH (sizeof(propListTable)/sizeof(propListTable[0]))

//static const IotPropName_e sIotCleanActionProps[] = {IOT_PROP_G_MATERIAL_CLEAN_SLOT};
//static const IotPropName_e sIotStartShakeActionProps[] = {};

static const IotActionProtoType_t sIotActionTable[IOT_ACTION_MAX] =
{
    //IOT_ACTION_RESET
	{7,1, 0, 0, NULL, NULL},


	//IOT_ACTION_PLUGIN_CONNECT
	{9,1, 0, 0, NULL, NULL},

	//IOT_ACTION_PLUGIN_DISCONNECT
	{9,2, 0, 0, NULL, NULL},


};



static const IotPropName_e datalogging_list[] =
{
    IOT_DL_PROP_INVERTERFAULT,          //10.12
    IOT_DL_PROP_SYSTEMSTATUS,           //10.13
    IOT_DL_PROP_DEFROSTREASON,          //10.14
    IOT_DL_PROP_DEFROSTSTEP,            //10.15
    IOT_DL_PROP_COMPRESSORSTATUS,       //10.16
    IOT_DL_PROP_FREEZERSTATUS,          //10.17
    IOT_DL_PROP_ROOMFREEZERSTATE,       //10.18
    IOT_DL_PROP_REFFANLEVEL,            //10.19    
    IOT_DL_PROP_COMPFEEDBACKFREQ,       //10.20
    IOT_DL_PROP_COMPFEEDBACKPOWER,      //10.21
    IOT_DL_PROP_COMPFEEDBACKVOL,        //10.22
    IOT_DL_PROP_TOTALRUNTIME,           //10.23
    IOT_DL_PROP_COMPTOTALTIME,          //10.24
    IOT_DL_PROP_COMRUNTIME,             //10.25
    IOT_DL_PROP_DEFROSTPRETIME,         //10.26
    IOT_DL_PROP_FRZHEATERONTIME,        //10.27
    IOT_DL_PROP_SUPERMODETIME,          //10.28
    IOT_DL_PROP_DEFROSTRUNTIME,         //10.29
    IOT_DL_PROP_HEATERSTATUS,           //10.30
    IOT_DL_PROP_ICELOADSTATUS,          //10.31
    IOT_DL_PROP_ICEMAKINGSTATUS,        //10.32
    IOT_DL_PROP_ICETARGETTIME,          //10.33
    IOT_DL_PROP_ICERUNTIME,             //10.34
    IOT_DL_PROP_ICECURRENTTIME,         //10.35
    IOT_DL_PROP_ERRORQUERY,             //10.36
    IOT_DL_PROP_VALVE_STATUS,           //10.37
    IOT_DL_PROP_REF_DOOR_TIMES,         //10.38
    IOT_DL_PROP_REF_DOOR_TIMER,         //10.39
    IOT_DL_PROP_FRZ_DOOR_TIMES,         //10.40
    IOT_DL_PROP_FRZ_DOOR_TIMER,         //10.41
};

static const IotPropName_e factorydata_list[] =
{
//	IOT_FCT_PROP_STATUS, IOT_FCT_PROP_LEFT_TIME, IOT_FCT_PROP_RUN_STATUS, IOT_FCT_PROP_TEMPERATURE, IOT_FCT_PROP_UNBALANCED_TRY_NUMS,
//	IOT_FCT_PROP_WEIGHING_VALUE, IOT_FCT_PROP_WATER_LEVEL_FREQ, IOT_FCT_PROP_PRESPIN_OOB, IOT_FCT_PROP_MAINSPIN_OOB, IOT_FCT_PROP_TARRGET_SPIN_SPEED,
//	IOT_FCT_PROP_SPIN_SPEED, IOT_FCT_PROP_DETERGENT_DELI_TIME, IOT_FCT_PROP_SOFTENER_DELI_TIME, IOT_FCT_PROP_COMPONENTS_STATUS
};

static const IotPropName_e foodRemindNotice_list[] = {IOT_PROP_FOOD_ALARM};

static const IotEventProtoType_t sIotEventTable[IOT_EVENT_MAX] =
{
//	//IOT_EVENT_WASH_END
//	{2, 1, 0, NULL},

//	//IOT_EVENT_START_DATALOGGING
//	{8, 1, 11, (const IotPropName_e*)&datalogging_list},

//	//IOT_EVENT_END_DATALOGGING
//	{8, 2, 12, (const IotPropName_e*)&datalogging_list},

//	//IOT_EVENT_DATA_REPORT
//	{10, 1, 14, (const IotPropName_e*)&factorydata_list},

	//IOT_EVENT_RESET
	{7, 1, 0, NULL},
    
    //IOT_EVENT_ICE_CLEAN_FINISH
    {8, 1, 0, NULL},
    
    //IOT_EVENT_FOOD_REMIND_NOTICE
    {9, 1, 1, (const IotPropName_e*) &foodRemindNotice_list},
    
    //IOT_EVENT_DATALOGGING
    {10, 1, 30, (const IotPropName_e*)&datalogging_list},

};


static TBool check_fridge_param_valid(IotPropName_e propName, IotGeneralPropVal_t* pValue)
{

	if (sIotPropStateTable[propName].propType != pValue->propType)
		return FALSE;

	if (pValue->propType == IOT_TYPE_STRING)
		return TRUE;

	TLong value = pValue->lValue;

	// if ((sIotPropStateTable[propName].propPolicy & PROP_POLICY_LIST_VALUE) != 0)
	// {

	// 	for (TByte i=0; i<PROPLIST_TABLE_LENTH; i++)
	// 	{
	// 		if (propListTable[i].name == propName)
	// 		{
	// 			IotPropList_t* pData=propListTable[i].pData;

	// 			for (TByte j = 0; j<propListTable[i].size; j++)
	// 			{
	// 				if (value == pData->value)
	// 					return TRUE;

	// 				pData++;
	// 			}

	// 			return FALSE;
	// 		}
	// 	}

	// 	return FALSE;

	// }
	// else
	//{

		if (value >= sIotPropValueCheckTable[propName].minVal && value <= sIotPropValueCheckTable[propName].maxVal)
		{
            return TRUE;//当有步进>1的值时，需要放出下面的代码
			TWord offsetValue;

			if (sIotPropValueCheckTable[propName].stepVal == 0)
				return TRUE;

			offsetValue = value - sIotPropValueCheckTable[propName].minVal;
            if (offsetValue == 0)
            {
                return TRUE;
            }

			if (offsetValue%sIotPropValueCheckTable[propName].stepVal == 0)
            {
				return TRUE;
            }

		}
	//}      

	return FALSE;

}

static void copy_fridge_param_from_iot_to_dev(IotPropName_e propName)
{
	void* pDestPtr = sIotPropStateTable[propName].p_DevDataAddress;
	void* pSrcPtr = sIotPropStateTable[propName].p_IotDataAddress;;


	switch(sIotPropStateTable[propName].propType)
	{
		case IOT_TYPE_BYTE:
		case IOT_TYPE_BOOL:
		{
			*((TByte*)pDestPtr) = *((TByte*)pSrcPtr);
			break;
		}        
        case IOT_TYPE_SHORT:
		{
			*((TShort*)pDestPtr) = *((TShort*)pSrcPtr);			
			break;
		}
		case IOT_TYPE_WORD:
		{
			*((TWord*)pDestPtr) = *((TWord*)pSrcPtr);			
			break;
		}
		case IOT_TYPE_LONG:
		{
			*((TLong*)pDestPtr) = *((TLong*)pSrcPtr);			
			break;
		}
		case IOT_TYPE_STRING:
		{
			strncpy((TChar*)pDestPtr, (TChar*)pSrcPtr, strlen((TChar*)pSrcPtr));
			break;
		}
		default:
			break;
	}

}

// TBool map_list_idx_and_val(IotPropName_e propName, TLong srcValue, TLong* dstValue, TBool id_to_val)
// {
// 	if (propName >= IOT_PROP_MAX)
// 		return FALSE;

// 	// if ((sIotPropStateTable[propName].propPolicy & PROP_POLICY_LIST_VALUE) == 0)
// 	// 	return FALSE;

// 	for (TByte i=0; i<PROPLIST_TABLE_LENTH; i++)
// 	{
// 		if (propListTable[i].name == propName)
// 		{
// 			IotPropList_t* pData=propListTable[i].pData;

// 			for (TByte j = 0; j<propListTable[i].size; j++)
// 			{
// 				if (id_to_val == true)
// 				{
// 					if (srcValue == pData->key)
// 					{
// 						*dstValue = pData->value;
// 						return TRUE;
// 					}
// 				}
// 				else
// 				{
// 					if (srcValue == pData->value)
// 					{
// 						*dstValue = pData->key;
// 						return TRUE;
// 					}
// 				}

// 				pData++;
// 			}

// 			return FALSE;
// 		}
// 	}

// 	return FALSE;

// }


// void reset_all_prop_to_unreported(void)
// {
// 	for (IotPropName_e prop = IOT_PROP_MIN; prop < IOT_PROP_MAX; prop++)
// 	{
// //		if (prop != IOT_PROP_G_FACTORY_DEADLINE)
// //			sIotPropStateTable[prop].propState = PROP_FLAG_NOT_REPORT_AFTER_POWER_ON|PROP_FLAG_NOT_REPORT_AFTER_BIND;
// 	}
//}

void reset_all_prop_updated_by_plugin(void)
{
	for (IotPropName_e prop = IOT_PROP_MIN; prop < IOT_PROP_MAX; prop++)
	{ 
		sIotPropStateTable[prop].propState &= ~(PROP_FLAG_UPDATED_WHEN_APP_CONNECT);
	}

}

TBool is_prop_updated_by_plugin(IotPropName_e propName)
{
	if (propName >= IOT_PROP_MAX)
		return TRUE;

	return (sIotPropStateTable[propName].propState & PROP_FLAG_UPDATED_WHEN_APP_CONNECT) > 0?TRUE:FALSE;

}

void set_prop_updated_by_plugin(IotPropName_e propName)
{

	if (propName < IOT_PROP_MAX)
	{
		sIotPropStateTable[propName].propState |= (PROP_FLAG_UPDATED_BY_IOT|PROP_FLAG_UPDATED_WHEN_APP_CONNECT);
	}
}

TBool is_result_successed(IotExecuteRet_e result)
{
	if (result == IOT_EXECUTE_OK || result == IOT_EXECUTE_DUP_VALUE)
		return TRUE;


	return  FALSE;
}

TBool need_sync_fridge_param_to_dev(IotExecuteRet_e set_result, IotPropName_e propName)
{
	if (propName >= IOT_PROP_MAINTENANCE_SECTION1 && propName <= IOT_PROP_MAINTENANCE_SECTION8)
	{
		return FALSE;
	}

	if (set_result == IOT_EXECUTE_OK)
		return TRUE;

	if (set_result == IOT_EXECUTE_DUP_VALUE)
	{
		return is_fridge_param_policy_matched(propName, PROP_POLICY_REPORT_WHEN_VALUE_DUP);
	}

	return FALSE;
}

TBool check_fridge_param_need_copy(IotPropName_e propName)
{
	void* pDevDestPtr = NULL;
	void* pIotDestPtr = NULL;

	if (propName >= IOT_PROP_MAX)
		return FALSE;

	pDevDestPtr = sIotPropStateTable[propName].p_DevDataAddress;
	pIotDestPtr = sIotPropStateTable[propName].p_IotDataAddress;

	switch(sIotPropStateTable[propName].propType)
	{
		case IOT_TYPE_BYTE:
		case IOT_TYPE_BOOL:
		{	
			if (*((TByte*)pIotDestPtr) != *((TByte*)pDevDestPtr))
			{
				return TRUE;
			}
			break;
		}	
        case IOT_TYPE_SHORT:
		{
			if (*((TShort*)pIotDestPtr) != *((TShort*)pDevDestPtr))
			{
				return TRUE;
			}
			break;

		}
		case IOT_TYPE_WORD:
		{
			if (*((TWord*)pIotDestPtr) != *((TWord*)pDevDestPtr))
			{
				return TRUE;
			}
			break;

		}
		case IOT_TYPE_LONG:
		{
			if (*((TLong*)pIotDestPtr) != *((TLong*)pDevDestPtr))
			{
				return TRUE;
			}
			break;

		}
		case IOT_TYPE_STRING:
		{
			if (strncmp((TChar*)pDevDestPtr, (TChar*)pIotDestPtr, strlen((TChar*)pDevDestPtr)))
			{
				return TRUE;
			}
			break;

		}
		default:
			break;
	}

	return FALSE;
}

void staging_fridge_param_rawdata(IotPropName_e propName, IotGeneralPropVal_t* pValue)
{
	void* pRealDestPtr = sIotPropStateTable[propName].p_IotDataAddress;

	if (sIotPropStateTable[propName].propType <= IOT_TYPE_LONG && pValue->propType == IOT_TYPE_LONG)
	{
		pValue->propType = sIotPropStateTable[propName].propType;
	}	    

	switch(pValue->propType)
	{
		case IOT_TYPE_BYTE:
		case IOT_TYPE_BOOL:
		{
			*((TByte*)pRealDestPtr) = (TByte)pValue->lValue;
			break;
		}
        case IOT_TYPE_SHORT:
		{
			*((TShort*)pRealDestPtr) = (TShort)pValue->lValue;
			break;
		}
		case IOT_TYPE_WORD:
		{
			*((TWord*)pRealDestPtr) = (TWord)pValue->lValue;
			break;
		}
		case IOT_TYPE_LONG:
		{
			*((TLong*)pRealDestPtr) = (TLong)pValue->lValue;
			break;
		}
		case IOT_TYPE_STRING:
		{
			strncpy((TChar*)pRealDestPtr, pValue->sValue, strlen(pValue->sValue));				
			break;
		}
		default:
			break;
	}

}


IotExecuteRet_e set_fridge_param_rawdata(IotPropName_e propName, IotGeneralPropVal_t* pValue, IotDataDir_e dir)
{
	IotExecuteRet_e ret = IOT_EXECUTE_OK;
	void* pDevDestPtr = sIotPropStateTable[propName].p_DevDataAddress;
	void* pRealDestPtr = pDevDestPtr;

	if (dir == IOT_DIR_IOT_TO_DEV)	
		pRealDestPtr = sIotPropStateTable[propName].p_IotDataAddress;

	if (propName >= IOT_PROP_MAX)
		return IOT_EXECUTE_FAIL;

	if (pValue == NULL)
		return IOT_EXECUTE_FAIL;

	if (dir == IOT_DIR_IOT_TO_DEV && sIotPropValueCheckTable[propName].bWritable == FALSE)
		return IOT_EXECUTE_CANNOT_WRITE;

	if (sIotPropStateTable[propName].propType <= IOT_TYPE_LONG && pValue->propType == IOT_TYPE_LONG)
	{
		pValue->propType = sIotPropStateTable[propName].propType;
	}		


	if (check_fridge_param_valid(propName, pValue) == FALSE)
		return IOT_EXECUTE_INVALID_VALUE;

#if 0
	if (dir == IOT_DIR_IOT_TO_DEV && (sIotPropStateTable[propName].propPolicy & PROP_POLICY_LIST_VALUE != 0))
	{
		TLong tValue = value;

		if (!map_wash_param_to_list_val(propName, tValue, &value))
			return IOT_EXECUTE_INVALID_VALUE;
	}
#endif

	switch(pValue->propType)
	{
		case IOT_TYPE_BYTE:
		case IOT_TYPE_BOOL:
		{
			if (pValue->lValue != *((TByte*)pDevDestPtr))
			{
				*((TByte*)pRealDestPtr) = (TByte)pValue->lValue;
			}
			else
			{
				ret = IOT_EXECUTE_DUP_VALUE;
			}
			break;
		}       
        case IOT_TYPE_SHORT:
		{
			if (pValue->lValue != *((TShort*)pDevDestPtr))
			{
				*((TShort*)pRealDestPtr) = (TShort)pValue->lValue;
			}
			else
			{
				ret = IOT_EXECUTE_DUP_VALUE;
			}

			break;
		}
		case IOT_TYPE_WORD:
		{
			if (pValue->lValue != *((TWord*)pDevDestPtr))
			{
				*((TWord*)pRealDestPtr) = (TWord)pValue->lValue;
			}
			else
			{
				ret = IOT_EXECUTE_DUP_VALUE;
			}

			break;
		}
		case IOT_TYPE_LONG:
		{
			if (pValue->lValue != *((TLong*)pDevDestPtr))
			{
				*((TLong*)pRealDestPtr) = (TLong)pValue->lValue;
			}
			else
			{
				ret = IOT_EXECUTE_DUP_VALUE;
			}

			break;
		}
		case IOT_TYPE_STRING:
		{
			if (!strncmp((TChar*)pDevDestPtr, pValue->sValue, strlen(pValue->sValue)))
			{
				ret = IOT_EXECUTE_DUP_VALUE;
			}
			else
			{
				strncpy((TChar*)pRealDestPtr, pValue->sValue, strlen(pValue->sValue));				
			}


#if 0		
			TChar* pStr = pValue->sValue;
			TChar* pDestPtr;
			TByte len = strlen(pStr);

			if (len > PROP_STRING_MAX_LENGTH)
			{
				ret = IOT_EXECUTE_INVALID_VALUE;
			}
			else
			{

				pDestPtr = (TChar*)sIotPropStateTable[propName].p_DevDataAddress;

				if (!strncmp(pDestPtr, pStr, len))
				{
					ret = IOT_EXECUTE_DUP_VALUE;
				}
				else
				{
					if (dir == IOT_DIR_IOT_TO_DEV)
						pDestPtr = (TChar*)sIotPropStateTable[propName].p_IotDataAddress;

					strncpy(pDestPtr, pStr, len);
				}
			}
#endif
			break;
		}
		default:
			return IOT_EXECUTE_FAIL;
	}

	if (ret == IOT_EXECUTE_DUP_VALUE)
	{
		if (sIotPropStateTable[propName].propState & PROP_FLAG_NOT_REPORT_AFTER_POWER_ON)
		{
			sIotPropStateTable[propName].propState &= ~PROP_FLAG_NOT_REPORT_AFTER_POWER_ON;

            if (sIotPropStateTable[propName].propState & PROP_FLAG_NOT_REPORT_AFTER_BIND)
            {
    			sIotPropStateTable[propName].propState &= ~PROP_FLAG_NOT_REPORT_AFTER_BIND;

                if (!(sIotPropStateTable[propName].propPolicy & PROP_POLICY_REPORT_WHEN_VALUE_DUP))
                {
                    sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
                }
            }
            
			if (dir == IOT_DIR_IOT_TO_DEV)
				sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
			else
				sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_DEV;

		}
		else if ((dir == IOT_DIR_IOT_TO_DEV) && (sIotPropStateTable[propName].propState & PROP_FLAG_UPDATED_BY_DEV))
		{
			sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
		}

	}

	return ret;
}

IotExecuteRet_e set_fridge_param(IotPropName_e propName, IotGeneralPropVal_t* pValue, IotDataDir_e dir)
{
	IotExecuteRet_e ret = set_fridge_param_rawdata(propName, pValue, dir);

	if (ret == IOT_EXECUTE_OK)
	{
		if (dir == IOT_DIR_DEV_TO_IOT)
		{
			sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_DEV;

//			if (propName == IOT_PROP_FAULT)
//			{
//				if (pValue->lValue != IOT_F_NONE)
//				{
//					trig_fridge_event(EVENT_TRIG_NEW_FAULT);
//				}
//				else
//				{
//					trig_fridge_event(EVENT_FAULT_CLEAR);
//				}
//			}
		}
	}

	return ret;
}


IotExecuteRet_e get_fridge_param_rawdata(IotPropName_e propName, IotGeneralPropVal_t* pValue, IotDataDir_e dir)
{
	IotExecuteRet_e ret = IOT_EXECUTE_OK;
	void* pDestPtr;


	if (propName >= IOT_PROP_MAX)
		return IOT_EXECUTE_FAIL;

	if (pValue == NULL)
		return IOT_EXECUTE_FAIL;

	pValue->propType = sIotPropStateTable[propName].propType;

	if (dir == IOT_DIR_IOT_TO_DEV)
		pDestPtr = sIotPropStateTable[propName].p_IotDataAddress;
	else
		pDestPtr = sIotPropStateTable[propName].p_DevDataAddress;	

	if (pDestPtr == NULL)
		return IOT_EXECUTE_INDIRECT_VALUE;

	switch(pValue->propType)
	{
		case IOT_TYPE_BYTE:
		case IOT_TYPE_BOOL:
		{
			pValue->lValue = *((TByte*)pDestPtr);
			break;
		}		
        case IOT_TYPE_SHORT:
		{
			pValue->lValue = *((TShort*)pDestPtr);
			break;
		}
		case IOT_TYPE_WORD:
		{
			pValue->lValue = *((TWord*)pDestPtr);
			break;
		}
		case IOT_TYPE_LONG:
		{
			pValue->lValue = *((TLong*)pDestPtr);
			break;
		}
		case IOT_TYPE_STRING:
		{
			strncpy(pValue->sValue, (TChar*)pDestPtr, strlen((TChar*)pDestPtr));
			break;
		}
		default:
			return IOT_EXECUTE_FAIL;
	}

	return ret;

}

IotExecuteRet_e get_fridge_param(IotPropName_e propName, IotGeneralPropVal_t* pValue, IotDataDir_e dir)
{
	IotExecuteRet_e ret;


	ret = get_fridge_param_rawdata(propName, pValue, dir);


	return ret;
}

IotPropName_e get_propname_by_spec_id(TByte siid, TByte piid)
{
	IotPropName_e wash_prop = IOT_PROP_MIN;

	for (;wash_prop < IOT_PROP_MAX; wash_prop++)
	{
		if (sIotPropStateTable[wash_prop].siid == siid && \
			sIotPropStateTable[wash_prop].piid == piid)
			return wash_prop;
	}

	return wash_prop;


}

TByte get_spec_siid_by_propname(IotPropName_e propName)
{
	if (propName < IOT_PROP_MAX)
	{
		return sIotPropStateTable[propName].siid;
	}

	return 0;
}

TByte get_spec_piid_by_propname(IotPropName_e propName)
{
	if (propName < IOT_PROP_MAX)
	{
		return sIotPropStateTable[propName].piid;
	}

	return 0;

}



IotActionName_e get_actionname_by_spec_id(TByte siid, TByte aiid)
{
	IotActionName_e wash_action = IOT_ACTION_MIN;

	for (;wash_action < IOT_ACTION_MAX; wash_action++)
	{
		if (sIotActionTable[wash_action].siid == siid && \
				sIotActionTable[wash_action].aiid == aiid)
			return wash_action;
	}

	return wash_action;


}



void reset_iot_setprop_excute_state(void)
{
	gIotFridge.commands_num = 0;
	memset(&gIotFridge.setprop_commands, 0, sizeof(gIotFridge.setprop_commands));
}


TBool prepare_iot_setprop_execute(TByte siid, TByte piid, IotGeneralPropVal_t* pValue)
{

	IotPropName_e propName = get_propname_by_spec_id(siid, piid);
	IotExecuteRet_e ret = IOT_EXECUTE_FAIL;


	if (propName < IOT_PROP_MAX)
	{
		// if (is_fridge_param_policy_matched(propName, PROP_POLICY_LIST_VALUE))
		// {
		// 	TLong lValue = pValue->lValue;

		// 	map_list_idx_and_val(propName, lValue, &(pValue->lValue), true);
		// }

		if (is_remote_control_allowed())
		{
			ret = set_fridge_param_by_iot(propName, pValue);
		}
		else
		{
			ret = IOT_EXECUTE_CHILD_PROTECTED;
		}

	}


	assemble_fridge_param_command(siid, piid, propName, ret);

	return (ret==IOT_EXECUTE_OK)?TRUE:FALSE;


}


void finish_iot_setprop_execute(IotPropName_e propName, TByte code)
{
	if (propName >= IOT_PROP_MAX)
		return;

	if (code == IOT_EXECUTE_OK)
	{
		sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;

		copy_fridge_param_from_iot_to_dev(propName);
	}
}


//wash param's type force to TLong
TLong* get_action_fridge_params(IotActionName_e actionName, TByte* len)
{

	if (actionName < IOT_ACTION_MAX)
	{
		TByte nums = sIotActionTable[actionName].param_nums;
		IotPropName_e propName;
		IotGeneralPropVal_t genPropVal;

		memset(gActionParams, 0, sizeof(gActionParams));

		for(TByte i = 0; i < nums; i++)
		{
			propName = sIotActionTable[actionName].props[i];

			get_fridge_param_setby_iot(propName, &genPropVal);

			gActionParams[i] = genPropVal.lValue;
		}

		*len = nums;

		return (TLong*)&gActionParams;
	}

	return NULL;
}

TByte get_action_fridge_out_param_num(IotActionName_e actionName)
{
	if (actionName < IOT_ACTION_MAX)
	{
		return sIotActionTable[actionName].out_nums;
	}

	return 0;
}

IotExecuteRet_e get_action_fridge_out_param_value(IotActionName_e actionName, TByte index, IotGeneralPropVal_t* pValue)
{
	if (actionName >= IOT_ACTION_MAX || index >= sIotActionTable[actionName].out_nums)
		return IOT_EXECUTE_FAIL;	

	return get_fridge_param_from_dev(sIotActionTable[actionName].results[index], pValue);

}


TByte get_spec_piid_by_action_out_index(IotActionName_e actionName, TByte index)
{
	if ((actionName < IOT_ACTION_MAX) && (index < sIotActionTable[actionName].out_nums))
	{
		return sIotPropStateTable[sIotActionTable[actionName].results[index]].piid;
	}

	return 0;

}




TBool prepare_iot_action_param(TByte siid, TByte piid, TByte index, IotGeneralPropVal_t* pValue)
{
	IotPropName_e propName;
	IotExecuteRet_e ret = IOT_EXECUTE_FAIL;
	IotActionName_e action = gIotFridge.action_command.action;


	if (index < sIotActionTable[action].param_nums)
	{
		propName = get_propname_by_spec_id(siid, piid);

		if (propName == sIotActionTable[action].props[index])
		{
			//TODO: check special value,255 means current value
			// if (is_fridge_param_policy_matched(propName, PROP_POLICY_LIST_VALUE))
			// {
			// 	TLong lValue = pValue->lValue;

			// 	map_list_idx_and_val(propName, lValue, &(pValue->lValue), true);
			// }

			staging_fridge_param_rawdata(propName, pValue);
			ret = IOT_EXECUTE_OK;
		}
	}

	if (!is_result_successed(ret))
	{
		gIotFridge.action_command.code = ret;
		gIotFridge.action_command.ready = true;
	}

	return is_result_successed(ret)?TRUE:FALSE;

}

TBool prepare_iot_action_execute(TByte siid, TByte aiid)
{
	IotActionName_e actionName = get_actionname_by_spec_id(siid, aiid);
	IotExecuteRet_e ret = IOT_EXECUTE_FAIL;

	gIotFridge.action_trig = TRUE;

	gIotFridge.action_command.siid =siid;
	gIotFridge.action_command.aiid =aiid;

	if (actionName < IOT_ACTION_MAX)
	{

		ret = IOT_EXECUTE_OK;			

		gIotFridge.action_command.action = actionName;

	}

	gIotFridge.action_command.code = ret;

	gIotFridge.action_command.ready = (ret==IOT_EXECUTE_OK)?false:true;	

	return (ret==IOT_EXECUTE_OK)?TRUE:FALSE;
}

void finish_iot_action_execute(IotActionName_e actionName, TByte code)
{
	if (actionName >= IOT_ACTION_MAX)
		return;

	if (actionName != gIotFridge.action_command.action)
		return;

	if (code == IOT_EXECUTE_OK)
	{
		TByte nums = sIotActionTable[actionName].param_nums;
		IotPropName_e propName;

		//TODO: consider action execute succ, but prop value is not equal to that passed by action
		for(TByte i = 0; i < nums; i++)
		{
			propName = sIotActionTable[actionName].props[i];

			if (check_fridge_param_need_copy(propName))
			{
				sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;

				copy_fridge_param_from_iot_to_dev(propName);
			}
			else
			{
				if (sIotPropStateTable[propName].propState & PROP_FLAG_NOT_REPORT_AFTER_POWER_ON)
				{
					sIotPropStateTable[propName].propState &= ~PROP_FLAG_NOT_REPORT_AFTER_POWER_ON;
					sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;

				}			
				else if (sIotPropStateTable[propName].propState & PROP_FLAG_UPDATED_BY_DEV)
				{
					sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
				}
			}

		}
	}

	gIotFridge.action_command.ready = true;
	
}


TByte get_event_report_param_num(IotEventName_e eventName)
{
	if (eventName >= IOT_EVENT_MAX)
		return 0;

	return sIotEventTable[eventName].param_nums;
}

IotExecuteRet_e get_event_report_param_value(IotEventName_e eventName, TByte index, callbackCustomGetFridgeParam callback, IotGeneralPropVal_t* pValue)
{
	if (eventName >= IOT_EVENT_MAX || index >= sIotEventTable[eventName].param_nums)
		return IOT_EXECUTE_FAIL;	

	if ((callback != NULL) && (callback(sIotEventTable[eventName].params[index], pValue) == IOT_EXECUTE_OK))
		return IOT_EXECUTE_OK;

	return get_fridge_param_from_dev(sIotEventTable[eventName].params[index], pValue);

}

TByte get_spec_siid_by_eventname(IotEventName_e eventName)
{
	if (eventName < IOT_EVENT_MAX)
	{
		return sIotEventTable[eventName].siid;
	}

	return 0;

}

TByte get_spec_eiid_by_eventname(IotEventName_e eventName)
{
	if (eventName < IOT_EVENT_MAX)
	{
		return sIotEventTable[eventName].eiid;
	}

	return 0;

}

static TByte get_spec_piid_by_factorydata_prop_name(IotPropName_e name)
{
	if(name >= IOT_FCT_PROP_MIN)
	{
		return name - IOT_FCT_PROP_MIN + 1;
	}
	
	return 0;
}

static TByte get_spec_piid_by_datalogging_prop_name(IotPropName_e name)
{
	if (name >= IOT_DL_PROP_MIN && name < IOT_FCT_PROP_MIN)
	{
		return name - IOT_DL_PROP_MIN + 1;
	}

	return 0;
}

TByte get_spec_piid_by_event_index(IotEventName_e eventName, TByte index)
{
	if ((eventName < IOT_EVENT_MAX) && (index < sIotEventTable[eventName].param_nums))
	{
		if (sIotEventTable[eventName].params[index] < IOT_PROP_MAX)
			return sIotPropStateTable[sIotEventTable[eventName].params[index]].piid;
		else if (sIotEventTable[eventName].params[index] < IOT_FCT_PROP_MIN)
			return get_spec_piid_by_datalogging_prop_name(sIotEventTable[eventName].params[index]);
		else
			return get_spec_piid_by_factorydata_prop_name(sIotEventTable[eventName].params[index]);
		
	}

	return 0;
}



TSWord map_result_to_iot_code(TByte result)
{
	TSWord code = -4003;

	switch(result)
	{
		case IOT_EXECUTE_OK:
		case IOT_EXECUTE_DUP_VALUE:
			code = 0;
			break;
		case IOT_EXECUTE_FAIL:
			code = -4003;
			break;
		case IOT_EXECUTE_INVALID_VALUE:
			code = -4005;
			break;
		case IOT_EXECUTE_CANNOT_WRITE:
			code = -4002;
			break;
		case IOT_EXECUTE_CANNOT_READ:
			code = -4001;
			break;
		case IOT_EXECUTE_DEV_FAIL:
			code = -4404;
			break;
		case IOT_EXECUTE_CHILD_PROTECTED:
			code = -4456;
			break;
	}

	return code;
}


TBool is_fridge_param_policy_matched(IotPropName_e propName, TByte policy)
{
	return (sIotPropStateTable[propName].propPolicy & policy) > 0?TRUE:FALSE;
}


TBool is_remote_control_allowed(void)
{
////	if ((get_dev_wash_param_by_name(global, child_protected_enabled) == false) || 
////		 (get_dev_wash_param_by_name(global, child_protected_status) == true))
	{
		 return TRUE;
	}

	return FALSE;
}

TBool is_fridge_param_updated(IotPropName_e propName)
{
	TByte flag;

	flag = PROP_FLAG_UPDATED_BY_IOT;

	if (propName >= IOT_PROP_MAX)
		return FALSE;

	if (gIotFridge.plugin_connect==TRUE)
	{
		if (sIotPropStateTable[propName].propPolicy & PROP_POLICY_REPORT_WHEN_VALUE_DUP)
		{
			flag = PROP_FLAG_UPDATED_BY_IOT;
		}
		else
		{
			flag = PROP_FLAG_UPDATED_BY_IOT|PROP_FLAG_UPDATED_BY_DEV;
		}
	}
	else
	{
		if (sIotPropStateTable[propName].propPolicy&PROP_POLICY_BASIC_INFO)
		{
			flag = PROP_FLAG_UPDATED_BY_IOT|PROP_FLAG_UPDATED_BY_DEV;
		}
		else
		{

			//if (gIotFridge.wash_dev_global.extended_func == TRUE)
////			if (get_dev_wash_param_by_name(global, extended_func) == TRUE)
////			{
////				if (sIotPropStateTable[propName].propPolicy&PROP_POLICY_REPORT_ON_EXTEND_FUNC)
////				{
////					flag = PROP_FLAG_UPDATED_BY_IOT|PROP_FLAG_UPDATED_BY_DEV;
////				}
////			}

		}
	}


	return (sIotPropStateTable[propName].propState & flag) > 0?TRUE:FALSE;

}


void clear_fridge_param_updated(IotPropName_e propName)
{
	if (propName >= IOT_PROP_MAX)
		return;

	sIotPropStateTable[propName].propState &= ~(PROP_FLAG_UPDATED_BY_IOT|PROP_FLAG_UPDATED_BY_DEV|PROP_FLAG_NOT_REPORT_AFTER_POWER_ON|PROP_FLAG_NOT_REPORT_AFTER_BIND);
}

void set_fridge_param_updated_by_event(IotPropName_e propName)
{

	if (propName < IOT_PROP_MAX)
	{
		sIotPropStateTable[propName].propState |= PROP_FLAG_UPDATED_BY_IOT;
	}
}

void set_fridge_param_uploading_by_event(IotPropName_e propName)
{
	if (propName < IOT_PROP_MAX)
	{
		sIotPropStateTable[propName].propState |= PROP_FLAG_UPLOADING;
	}
}

void clear_fridge_param_uploading(IotPropName_e propName)
{
	if (propName >= IOT_PROP_MAX)
	return;

	sIotPropStateTable[propName].propState &= ~PROP_FLAG_UPLOADING;
}

TBool is_fridge_param_uploading(IotPropName_e propName)
{
	if (propName < IOT_PROP_MAX)
	{
		return (sIotPropStateTable[propName].propState & PROP_FLAG_UPLOADING) ? true : false;
	}

	return false;
}


TBool is_allowed_execute_ota(void)
{
//	if (gIotFridge.wash_dev_down.status > IOT_S_IDLE &&
//		gIotFridge.wash_dev_down.status < IOT_S_COMPLETED)
//		return false;

//	if (gIotFridge.dev_global.fault > 0 ||
//		gIotFridge.dev_down.fault > 0)
//		return false;

//	if (is_dev_washing())
//		return false;

	return true;
	
}


