/*!
 * @file
 * @brief Manages all the fault code.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include <string.h>
#include "FaultCode.h"
#include "InverterUsart.h"
#include "Driver_AdSample.h"
#include "Driver_Fan.h"
#include "Defrosting.h"
#include "Driver_DoorSwitch.h"
#include "DisplayInterface.h"
#include "Sbus_Display.h"
#include "Sbus_IceMaker.h"
#include "Sbus_Nfc.h"
#include "ParameterManager.h"

static uint8_t ary_FaultCodeByte[(uint8_t)eFCode_Max];
static uint8_t ary_FaultCodeByteBak[(uint8_t)eFCode_Max];
static uint8_t u8_FaultCodeNumber;

// clang-format off
static const  DispFault_st ary_DispFault[] =
{ //  Fault Type            Fault Byte Index              Bit Index 
    { FAULT_REF_SNR,         (uint8_t)eFCode_IotByte0,    0x02 }, // 冷藏传感器故障
    { FAULT_REFVAR_SNR,      (uint8_t)eFCode_IotByte1,    0x04 }, // 变温传感器故障
    { FAULT_VAR_TOP_SNR,     (uint8_t)eFCode_IotByte2,    0x08 }, // 液晶顶传感器故障
    { FAULT_VAR_BOTTOM_SNR,  (uint8_t)eFCode_IotByte2,    0x04 }, // 液晶底传感器故障
    { FAULT_VAR_BOTTOMX_SNR, (uint8_t)eFCode_IotByte2,    0x10 }, // 液晶后底传感器故障
    { FAULT_FRZ_SNR,         (uint8_t)eFCode_IotByte0,    0x04 }, // 冷冻传感器故障
    { FAULT_REFDEF_SNR,      (uint8_t)eFCode_IotByte1,    0x40 }, // 冷藏化霜传感器故障
    { FAULT_DEF_SNR,         (uint8_t)eFCode_IotByte0,    0x10 }, // 冷冻化霜传感器故障
    { FAULT_ROOM_SNR,        (uint8_t)eFCode_IotByte0,    0x08 }, // 环境温度传感器故障
    { FAULT_REF_FAN,         (uint8_t)eFCode_IotByte1,    0x80 }, // 冷藏风机故障
    { FAULT_FRZ_FAN,         (uint8_t)eFCode_IotByte0,    0x20 }, // 冷冻风机故障
    { FAULT_COOL_FAN,        (uint8_t)eFCode_IotByte1,    0x02 }, // 冷凝风机故障
    { FAULT_DEF_FUNC,        (uint8_t)eFCode_IotByte0,    0x40 }, // 化霜故障
    { FAULT_INVERTER_COMM,   (uint8_t)eFCode_IotByte2,    0x01 }, // 变频板通信故障
    { FAULT_DOOR_REF_LEFT,   (uint8_t)eFCode_DoorSwitch,  0x01 }, // 冷藏左门超时
    { FAULT_DOOR_REF_RIGHT,  (uint8_t)eFCode_DoorSwitch,  0x02 }, // 冷藏右门超时
    { FAULT_DOOR_FRZ_LEFT,   (uint8_t)eFCode_DoorSwitch,  0x04 }, // 冷冻左门超时
    { FAULT_DOOR_FRZ_RIGHT,  (uint8_t)eFCode_DoorSwitch,  0x08 }, // 冷冻右门超时
    { FAULT_ICEMAKER_COMM,   (uint8_t)eFCode_IotByte2,    0x20 }, // 液晶板通信故障
};
// clang-format on

static void Collect_ActiveFaultCode(void)
{
    uint8_t index = 0;
    uint8_t bit_value = 0;
    uint8_t active_number = 0;

    if(memcmp(ary_FaultCodeByte, ary_FaultCodeByteBak, (uint8_t)eFCode_Max) != 0)
    {
        memcpy(ary_FaultCodeByteBak, ary_FaultCodeByte, (uint8_t)eFCode_Max);
        u8_FaultCodeNumber = 0;

        for(index = 0; index < sizeof(ary_DispFault) / sizeof(DispFault_st); index++)
        {
            bit_value = ary_FaultCodeByteBak[ary_DispFault[index].u8_ByteNum] & ary_DispFault[index].u8_BitValue;
            if(bit_value == ary_DispFault[index].u8_BitValue)
            {
                u8_FaultCodeNumber++;
            }
        }
    }

}

void Collect_FaultCode(void)
{
    bool err_state = false;
    uint8_t byte = 0;
    uint16_t fcode = 0;
    uint8_t mt = GetMachineType();

    byte = 0;
    err_state = Get_DisplayCommErr();
    BIT_WRITE(byte, BIT_INDEX_0, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_REF);
    BIT_WRITE(byte, BIT_INDEX_1, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_FRZ);
    BIT_WRITE(byte, BIT_INDEX_2, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_ROOM);
    BIT_WRITE(byte, BIT_INDEX_3, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_DEFROST);
    BIT_WRITE(byte, BIT_INDEX_4, err_state);
    err_state = Is_FanContinueError(FRZ_FAN);
    BIT_WRITE(byte, BIT_INDEX_5, err_state);
    err_state = Get_DefrostFunctionErrorReport();
    BIT_WRITE(byte, BIT_INDEX_6, err_state);
    ary_FaultCodeByte[(uint8_t)eFCode_IotByte0] = byte;

    byte = 0;
    err_state = Is_FanContinueError(COOL_FAN);
    BIT_WRITE(byte, BIT_INDEX_1, err_state);
    if(mt == MACHINE_TYPE_NORMAL)
    {
        err_state = Get_SensorError((uint8_t)SENSOR_VV);
        BIT_WRITE(byte, BIT_INDEX_2, err_state);
    }
    err_state = Get_SensorError((uint8_t)SENSOR_REF_DEFROST);
    BIT_WRITE(byte, BIT_INDEX_6, err_state);
    err_state = Is_FanContinueError((uint8_t)REF_FAN);
    BIT_WRITE(byte, BIT_INDEX_7, err_state);
    ary_FaultCodeByte[(uint8_t)eFCode_IotByte1] = byte;

    byte = 0;
    err_state = Get_InverterCommErr();
    BIT_WRITE(byte, BIT_INDEX_0, err_state);
    if(mt == MACHINE_TYPE_FRENCH ||
       mt == MACHINE_TYPE_CROSS)
    {
        err_state = GetIceMakerWorkErr();
        BIT_WRITE(byte, BIT_INDEX_1, err_state);
    }
    err_state = Get_SensorError((uint8_t)SENSOR_ICEMAKER_BOTTOM);
    BIT_WRITE(byte, BIT_INDEX_2, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_ICEMAKER_TOP);
    BIT_WRITE(byte, BIT_INDEX_3, err_state);
    err_state = Get_SensorError((uint8_t)SENSOR_ICEMAKER_BOTTOMX);
    BIT_WRITE(byte, BIT_INDEX_4, err_state);
    if(mt == MACHINE_TYPE_CROSS || mt == MACHINE_TYPE_FRENCH)
    {
        err_state = Get_IceMakerCommErr();
        BIT_WRITE(byte, BIT_INDEX_5, err_state);
        err_state = Get_NfcCommErr();
        BIT_WRITE(byte, BIT_INDEX_6, err_state);
    }
    ary_FaultCodeByte[(uint8_t)eFCode_IotByte2] = byte;

    byte = 0;
    err_state = Get_DoorSwitchAlarmState((uint8_t)DOOR_REF_LEFT);
    BIT_WRITE(byte, BIT_INDEX_0, err_state);
    err_state = Get_DoorSwitchAlarmState((uint8_t)DOOR_REF_RIGHT);
    BIT_WRITE(byte, BIT_INDEX_1, err_state);
    err_state = Get_DoorSwitchAlarmState((uint8_t)DOOR_FRZ_LEFT);
    BIT_WRITE(byte, BIT_INDEX_2, err_state);
    err_state = Get_DoorSwitchAlarmState((uint8_t)DOOR_FRZ_RIGHT);
    BIT_WRITE(byte, BIT_INDEX_3, err_state);
    ary_FaultCodeByte[(uint8_t)eFCode_DoorSwitch] = byte;

    byte = Get_CompErrorState();
    ary_FaultCodeByte[(uint8_t)eFCode_Inverter] = byte;

    fcode = GetParameterSnFaultCode();
    ary_FaultCodeByte[(uint8_t)eFCode_SnBYTE0] = fcode & 0xFF;
    ary_FaultCodeByte[(uint8_t)eFCode_SnBYTE1] = fcode >> 8;

    fcode = GetParameterFaultCode();
    ary_FaultCodeByte[(uint8_t)eFCode_ParamBYTE0] = fcode & 0xFF;
    ary_FaultCodeByte[(uint8_t)eFCode_ParamBYTE1] = fcode >> 8;

    Collect_ActiveFaultCode();
}

uint8_t Get_FaultCodeByte(uint8_t index)
{
    return (ary_FaultCodeByte[(FaultCodeByteIndex_t)index]);
}

uint8_t Get_FaultCodeNumber(void)
{
    return (u8_FaultCodeNumber);
}
