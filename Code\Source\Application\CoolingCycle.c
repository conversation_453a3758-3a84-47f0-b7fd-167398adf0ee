/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "CoolingCycle.h"
#include "FridgeRunner.h"
#include "Core_CallBackTimer.h"
#include "Parameter_TemperatureZone.h"
#include "Driver_AdSample.h"
#include "ResolverDevice.h"
#include "Driver_Fan.h"
#include "Drive_Valve.h"
#include "Driver_SingleDamper.h"
#include "SystemTimerModule.h"
#include "DisplayInterface.h"
#include "Sbus_IceMaker.h"
#include "Driver_Flash.h"
#include "Defrosting.h"
#include "CloudControl.h"

#define COOLING_CYCLE_POLL_SECONDS (uint16_t)1
#define NUM_COUNTS_PER_MINUTE (uint16_t)60
#define REF_COOLING_ON_SECOND (uint16_t)3600 // 60min 60x60=3600

enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
    Signal_CompStartup
};

static st_CoreCallbackTimer st_CoolingCycleTimer;
static SimpleFsm_t st_CoolingFsm;
static Cooling_st st_Cooling;
static CoolingCompState_t coolingCompState;
static ZoneCoolingState_t zoneCoolingState;
static CoolingCapacityState_t coolingCapacityState;
static uint16_t u16_CoolingOffTimerCounter;
static uint16_t u16_CoolingProtectTimerCounter;
static uint16_t u16_CoolingStartupTimerCounter;
static uint16_t u16_CoolingOnTimerCounter;
static uint16_t u16_CompOnTimerCounter;
static uint16_t u16_RefCoolingOnTimerCounter;
static uint16_t u16_CoolFanCondensationTimerCounter;
static uint16_t u16_ComOnStillRefCoolTimerStart;
static ValveState_em save_vstate = Valve_Initial;

static void CoolingState_CompOff(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void CoolingState_CompProtect(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void CoolingState_CompStartup(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void CoolingState_CompOn(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static void Process_RefVarZoneCoolingState(void)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool cooling_state = CoolingCycle_GetRefVarCoolingState();
    uint8_t fan_duty = Get_CompOffFanSettingIndex(room_range);
    linyun_power_param_st *lyparam = Get_LinYunPowerParam();
    uint8_t linyun_power = Get_LinYunPowerSave();

    if(true == cooling_state)
    {
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_VarDamper, DAMPER_AllOpen);
        Vote_DeviceStatus((uint8_t)FSM_DefaultControl, (uint8_t)DEVICE_FrzFan, fan_duty);
    }
    else
    {
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_VarDamper, DAMPER_AllClose);
        if(linyun_power != 0)
        {
            Vote_DeviceStatus((uint8_t)FSM_DefaultControl, (uint8_t)DEVICE_FrzFan, lyparam->frzfan);
        }
        else
        {
            Vote_DeviceStatus((uint8_t)FSM_DefaultControl, (uint8_t)DEVICE_FrzFan, DS_Off);
        }
		
        if(IsFastIceMakerFrzFanNeedRun())
        {
            Vote_DeviceStatus((uint8_t)FSM_DefaultControl, (uint8_t)DEVICE_FrzFan, fan_duty);
        }
    }
}

static void Expired_PollTimer(void)
{
    Process_RefVarZoneCoolingState();
    SimpleFsm_SendSignal(&st_CoolingFsm, Signal_PollTimerExpired, NULL);
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_CoolingCycleTimer,
        Expired_PollTimer,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_CoolingCycleTimer);
}

static bool CompOff_GetCompProtectState(void)
{
    bool b_protect_state = true;

    if(0 == st_Cooling.u16_CoolingCycleNumber)
    {
        b_protect_state = false;
    }

    return (b_protect_state);
}

static void CompOff_SetOutputState(ZoneCoolingState_t state)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_protect_state = CompOff_GetCompProtectState();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool varcooling_state = CoolingCycle_GetRefVarCoolingState();

    switch(state)
    {
        case(ZoneCoolingState_t)eZoneCooling_Idle:
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, FREQ_0HZ);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, 0);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefFan, 0);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, DS_DontCare);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, DAMPER_AllClose);

            if(b_energy_mode)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_Valve, Valve_AllClose);
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_Valve, Valve_FrzOFF_RefON);
            }
            break;
        case(ZoneCoolingState_t)eZoneCooling_Ref:
        case(ZoneCoolingState_t)eZoneCooling_Frz:
        case(ZoneCoolingState_t)eZoneCooling_RefFrz:
            if(false == st_Cooling.b_ValveReset)
            {
                if(false == b_protect_state)
                {
                    SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompStartup);
                }
                else
                {
                    if(COOLING_PROTECT_TIME_SECONDS <= u16_CoolingOffTimerCounter)
                    {
                        SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompStartup);
                    }
                    else
                    {
                        u16_CoolingProtectTimerCounter = u16_CoolingOffTimerCounter;
                        SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompProtect);
                    }
                }
            }
            break;
        default:
            break;
    }
}

static void InProcess_UpdateCapacityState(void)
{
    uint8_t icemaker_mode = eIceMaker_Stop_Mode;
    bool ref_high_load_flag = Get_RefTempHighLoadState();
    bool frz_high_load_flag = Get_FrzTempHighLoadState();
    bool var_high_load_flag = Get_VarTempHighLoadState();
    bool b_energy_mode = Get_EnergyConsumptionModeState();

    if((true == ref_high_load_flag) ||
       (true == frz_high_load_flag) ||
       (true == var_high_load_flag))
    {
        st_Cooling.b_HighLoad = true;
    }

    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);
    if(icemaker_mode == eIceMaker_Quick_Mode)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FastRunning;
    }
    else if(true == st_Cooling.b_TurboFreeze)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FastRunning;
    }
    else if(true == st_Cooling.b_DeepFreeze)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FastRunning;
    }
    else if(true == st_Cooling.b_TurboCool)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_HighLoad;
    }
    else if(true == b_energy_mode)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_EnergyMode;
    }
    else if(true == st_Cooling.b_HighLoad)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_HighLoad;
    }
    else
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_Normal;
    }
}

static void FirstCycle_UpdateCapacityState(void)
{
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool deforst_first_on_flag = Get_Deforst_First_On_Flag();

    if((CoolingEntryMode_t)eMode_FactoryCompleted == st_Cooling.coolingEntryMode)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FactoryCompleted;
    }
    else if((CoolingEntryMode_t)eMode_FridgePowerOn == st_Cooling.coolingEntryMode)
    {
        coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_FridgePowerOn;
    }
    else
    {
        if(true == b_energy_mode)
        {
            coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_EnergyMode;
        }
        else
        {
            coolingCapacityState = (CoolingCapacityState_t)eCoolingCapacity_HighLoad;
        }
    }
}

static void CoolingCycle_UpdateCapacityState(void)
{
    if(0 == st_Cooling.u16_CoolingCycleNumber)
    {
        FirstCycle_UpdateCapacityState();
    }
    else
    {
        InProcess_UpdateCapacityState();
    }
}

static void CompStartup_SetOutputState(ZoneCoolingState_t state)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t freq_index = Get_CompFreqIndex(coolingCapacityState, st_Cooling.u16_CompStillOnTimeMinute);
    uint8_t ref_fan_duty = Get_CompOnFrzFanSettingIndex(freq_index);
    uint8_t cond_fan_duty = Get_CondFanSettingIndex(freq_index);
    bool b_condensation_mode = Get_CondensationModeState();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    uint8_t frz_fan_duty = Get_CompOnFrzFanSettingIndex(freq_index);
    bool b_cool_fan_on = true;

    if(true == b_condensation_mode ||
        (room_range == RT_BELOW18 && b_energy_mode == false))
    {
        b_cool_fan_on = false;
    }

    switch(state)
    {
        case(ZoneCoolingState_t)eZoneCooling_Idle:
            SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompOff);
            break;
        case(ZoneCoolingState_t)eZoneCooling_Frz:
            if(u16_CoolingStartupTimerCounter < VLAVE_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_Valve, Valve_FrzON_RefOFF);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_RefFan, 0);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_FrzFan, 0);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, 0);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, DAMPER_AllClose);
            }
            else if(u16_CoolingStartupTimerCounter < FAN_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            }
            else if(u16_CoolingStartupTimerCounter < DAMPER_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, frz_fan_duty);
                if(b_cool_fan_on == true)
                {
                    Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
                }
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
                if(b_cool_fan_on == true)
                {
                    Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
                }
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, frz_fan_duty);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, DAMPER_AllClose);
            }
            break;
        case(ZoneCoolingState_t)eZoneCooling_Ref:
            if(u16_CoolingStartupTimerCounter < VLAVE_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_Valve, Valve_FrzOFF_RefON);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_RefFan, 0);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_FrzFan, DS_DontCare);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, 0);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, DAMPER_AllClose);
            }
            else if(u16_CoolingStartupTimerCounter < FAN_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            }
            else if(u16_CoolingStartupTimerCounter < DAMPER_DELAY_STARTUP_SECOND)
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefFan, ref_fan_duty);
                if(b_cool_fan_on == true)
                {
                    Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
                }
            }
            else
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
                if(b_cool_fan_on == true)
                {
                    Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
                }
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefFan, ref_fan_duty);
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, DAMPER_AllOpen);
            }
            break;
        default:
            break;
    }
}

static void Ctrl_CoolFanStatus(uint8_t freq_index)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    bool b_condensation_mode = Get_CondensationModeState();
    uint8_t cond_fan_duty = 0;

    if(false == b_condensation_mode)
    {
        cond_fan_duty = Get_CondFanSettingIndex(freq_index);
        if(b_energy_mode == false && room_range == RT_BELOW18)
        {
            if(u16_CoolingOnTimerCounter < (COOL_FAN_DELAY_ON_SECOND - DAMPER_DELAY_STARTUP_SECOND + VLAVE_DELAY_STARTUP_SECOND))
            {
                cond_fan_duty = 0;
            }
        }
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, cond_fan_duty);
    }
    else if(u16_CoolingOnTimerCounter < (CONDENSATION_COOL_FAN_DELAY_SECOND - DAMPER_DELAY_STARTUP_SECOND))
    {
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, 0);
    }
    else
    {
        u16_CoolFanCondensationTimerCounter++;
        if(u16_CoolFanCondensationTimerCounter <= CONDENSATION_COOL_FAN_ON_SECOND)
        {
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, CONDENSATION_COOL_FAN_DUTY);
        }
        else if(u16_CoolFanCondensationTimerCounter <= (CONDENSATION_COOL_FAN_ON_SECOND + CONDENSATION_COOL_FAN_OFF_SECOND))
        {
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_CoolFan, 0);
        }
        else
        {
            u16_CoolFanCondensationTimerCounter = 0;
        }
    }
}

static void CompOn_SetOutputState(ZoneCoolingState_t state)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t freq_index = Get_CompFreqIndex(coolingCapacityState, st_Cooling.u16_CompStillOnTimeMinute);
    uint8_t frz_fan_duty = Get_CompOnFrzFanSettingIndex(freq_index);
    uint8_t ref_fan_duty = Get_CompOnRefFanSettingIndex(freq_index);

    switch(state)
    {
        case(ZoneCoolingState_t)eZoneCooling_Idle:
            Set_RefFrostReduceMode();
            u16_RefCoolingOnTimerCounter = 0;
            SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompOff);
            break;
        case(ZoneCoolingState_t)eZoneCooling_Frz:

            Ctrl_CoolFanStatus(freq_index);
            if(u16_RefCoolingOnTimerCounter > 0)
            {
                Set_RefFrostReduceMode();
                u16_RefCoolingOnTimerCounter = 0;
            }
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefFan, 0);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_Valve, Valve_FrzON_RefOFF);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, frz_fan_duty);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, DAMPER_AllClose);
            break;
        case(ZoneCoolingState_t)eZoneCooling_Ref:
            if(u16_RefCoolingOnTimerCounter == 0)
            {
                Exit_RefFrostReduceMode();
            }
            u16_RefCoolingOnTimerCounter++;
            Ctrl_CoolFanStatus(freq_index);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, DEVICE_Valve, Valve_FrzOFF_RefON);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_Comp, freq_index);
            if(Get_FrzFanOnState())
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, frz_fan_duty);
            }
            else if(Get_FrzFanOffState())
            {
                Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_FrzFan, DS_DontCare);
            }
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefFan, ref_fan_duty);
            Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_RefDamper, DAMPER_AllOpen);
            break;
        default:
            break;
    }
}

static void CoolingState_CompOff(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            u16_CoolingOffTimerCounter = 0;
            if((CoolingCompState_t)eCooling_CompOn == coolingCompState)
            {
                st_Cooling.b_PullDown = false;
                st_Cooling.b_HighLoad = false;
                st_Cooling.u16_CoolingCycleNumber++;
            }
            st_Cooling.b_ValveReset = false;
            st_Cooling.b_ValveRestAction = false;
            Clear_ValveStayRefForstState();
            coolingCompState = (CoolingCompState_t)eCooling_CompOff;
            CompOff_SetOutputState(eZoneCooling_Idle);
            break;
        case Signal_PollTimerExpired:
            u16_CoolingOffTimerCounter++;
            if(false == st_Cooling.b_ValveReset && 
               Driver_ValveNeedReset())
            {
                st_Cooling.b_ValveReset = true;
            }
            else if(st_Cooling.b_ValveReset && 
                    st_Cooling.b_ValveRestAction == false &&
                    u16_CoolingOffTimerCounter > VLAVE_RESET_PROTECT_SECOND)
            {
                st_Cooling.b_ValveRestAction = true;
                Drive_ValveReset();
            }
            else if(st_Cooling.b_ValveReset &&
                    st_Cooling.b_ValveRestAction &&
                    false == Driver_ValveNeedReset())
            {
                st_Cooling.b_ValveReset = false;
            }
            zoneCoolingState = CompOff_GetZoneCoolingState();
            if(zoneCoolingState != eZoneCooling_Frz &&
                zoneCoolingState != eZoneCooling_Idle &&
                Get_RefFrostReduceMode())
            {
                Exit_RefFrostReduceMode();
            }
            CompOff_SetOutputState(zoneCoolingState);
            break;
        case Signal_Exit:
            u16_CoolingOffTimerCounter = 0;
            break;
        default:
            break;
    }
}

static void CoolingState_CompProtect(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            coolingCompState = (CoolingCompState_t)eCooling_CompProtect;
            break;
        case Signal_PollTimerExpired:
            u16_CoolingProtectTimerCounter++;
            if(COOLING_PROTECT_TIME_SECONDS <= u16_CoolingProtectTimerCounter)
            {
                SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompStartup);
            }
            break;
        case Signal_Exit:
            u16_CoolingProtectTimerCounter = 0;
            break;
        default:
            break;
    }
}

static void CoolingState_CompStartup(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            coolingCompState = (CoolingCompState_t)eCooling_CompStartup;
            u16_CoolingStartupTimerCounter = 0;
            zoneCoolingState = CompOn_GetZoneCoolingState();
            if(zoneCoolingState == eZoneCooling_Ref)
            {
                Exit_RefFrostReduceMode();
            }
            break;
        case Signal_PollTimerExpired:
            u16_CoolingStartupTimerCounter++;
            u16_RefCoolingOnTimerCounter = 0;
            CoolingCycle_UpdateCapacityState();
            CompStartup_SetOutputState(zoneCoolingState);
            if(DAMPER_DELAY_STARTUP_SECOND <= u16_CoolingStartupTimerCounter)
            {
                SimpleFsm_Transition(&st_CoolingFsm, CoolingState_CompOn);
            }
            break;
        case Signal_Exit:
            u16_CoolingStartupTimerCounter = 0;
            break;
        default:
            break;
    }
}

static void CompOn_ModifyCoolingState(void)
{
    uint16_t timeout = COMP_ON_CONTINUE_SECONDS;

    if(st_Cooling.coolingEntryMode == eMode_DefrostingCompleted &&
       st_Cooling.u16_CoolingCycleNumber == 0 &&
       st_Cooling.b_CompOnFirstRefCool == false)
    {
        timeout = DEFORST_COMP_ON_CONTINUE_SECONDS;
    }

    if(IsRoomTempBelowEightDegree() && st_Cooling.b_CompOnStillRefCool == false)
    {
        if(u16_CompOnTimerCounter > timeout)
        {
            u16_ComOnStillRefCoolTimerStart = Get_MinuteCount();
            st_Cooling.b_CompOnStillRefCool = true;
        }
    }

    if(st_Cooling.b_CompOnStillRefCool &&
        Get_MinuteElapsedTime(u16_ComOnStillRefCoolTimerStart) < COMP_ON_CONTINUE_REF_COOL_MINUTES)
    {
        st_Cooling.b_CompOnFirstRefCool = true;
        if(zoneCoolingState == eZoneCooling_Frz)
        {
            zoneCoolingState = eZoneCooling_Ref;
        }
    }
    else if(st_Cooling.b_CompOnStillRefCool)
    {
        st_Cooling.b_CompOnStillRefCool = false;
        u16_CompOnTimerCounter = 0;
    }
}

static void Check_ValvePosition(void)
{
    ValveState_em vstate;
    bool is_sensor_error;
    uint16_t valve_minutes;
    uint16_t ref_deforst_temp;
    static uint16_t valve_start;
    static uint16_t save_ref_deforst_temp;

    vstate = Get_ValveState();
    is_sensor_error = Get_SensorError(SENSOR_REF_DEFROST);
    ref_deforst_temp = Get_SensorValue(SENSOR_REF_DEFROST);
    if(is_sensor_error == false)
    {
        if(save_vstate != vstate)
        {
            save_vstate = vstate;
            save_ref_deforst_temp = ref_deforst_temp;
            valve_start = Driver_ValveStateGetStayTime(save_vstate);
        }

        valve_minutes = Driver_ValveStateGetStayTime(save_vstate);
        if(valve_minutes < valve_start)
        {
            save_vstate = Valve_Initial;
            return;
        }

        if(save_vstate == Valve_FrzOFF_RefON &&
           ref_deforst_temp >= (save_ref_deforst_temp - 10) &&
           (valve_minutes - valve_start) >= U16_REF_VLAVE_CHECK_TIME_MINUTES)

        {
            Driver_ValveForceNeedReset();
            zoneCoolingState = eZoneCooling_Idle;
            save_vstate = Valve_Initial;
        }

        if(save_vstate == Valve_FrzON_RefOFF &&
           ref_deforst_temp <= (save_ref_deforst_temp - 30) &&
           (valve_minutes - valve_start) >= U16_FRZ_VLAVE_CHECK_TIME_MINUTES)

        {
            Driver_ValveForceNeedReset();
            zoneCoolingState = eZoneCooling_Idle;
            save_vstate = Valve_Initial;
        }
    }
    else
    {
        save_vstate = Valve_Initial;
    }
}

static void CoolingState_CompOn(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    switch(signal)
    {
        case Signal_Entry:
            coolingCompState = (CoolingCompState_t)eCooling_CompOn;
            u16_CoolingOnTimerCounter = 0;
            u16_CompOnTimerCounter = 0;
            u16_RefCoolingOnTimerCounter = 0;
            u16_CoolFanCondensationTimerCounter = 0;
            u16_ComOnStillRefCoolTimerStart = 0;
            st_Cooling.b_CompOnStillRefCool = false;
            st_Cooling.b_CompOnFirstRefCool = false;
            st_Cooling.u16_CompStillOnTimeStart = Get_MinuteCount();
            st_Cooling.u16_CompStillOnTimeMinute = 0;
            st_Cooling.u16_CompTotalOnTimeSuspend = st_Cooling.u16_CompTotalOnTimeMinute;
            save_vstate = Valve_Initial;
            Driver_ValveStateClearStayTime();
            break;
        case Signal_PollTimerExpired:
            u16_CoolingOnTimerCounter++;
            u16_CompOnTimerCounter++;
            zoneCoolingState = CompOn_GetZoneCoolingState();
            CompOn_ModifyCoolingState();
            CoolingCycle_UpdateCapacityState();
            st_Cooling.u16_CompStillOnTimeMinute = Get_MinuteElapsedTime(st_Cooling.u16_CompStillOnTimeStart);
            st_Cooling.u16_CompTotalOnTimeMinute =
                st_Cooling.u16_CompTotalOnTimeSuspend + st_Cooling.u16_CompStillOnTimeMinute;
            Check_ValvePosition();
            CompOn_SetOutputState(zoneCoolingState);
            break;
        case Signal_Exit:
            u16_CoolingOnTimerCounter = 0;
            u16_RefCoolingOnTimerCounter = 0;
            u16_CoolFanCondensationTimerCounter = 0;
            st_Cooling.u16_CompStillOnTimeMinute = 0;
            st_Cooling.u16_CompTotalOnTimeSuspend = st_Cooling.u16_CompTotalOnTimeMinute;
            break;
        default:
            break;
    }
}

void CoolingCycle_Init(CoolingEntryMode_t mode)
{
    RoomTempRange_t room_range = Get_RoomTempRange();

    zoneCoolingState = (ZoneCoolingState_t)eZoneCooling_Idle;
    coolingCompState = (CoolingCompState_t)eCooling_CompOff;
    st_Cooling.u16_CoolingCycleNumber = 0;
    st_Cooling.coolingEntryMode = mode;
    uint8_t comp_freq = Get_ResolvedDeviceStatus(DEVICE_Comp);

    if((CoolingEntryMode_t)eMode_FridgePowerOn == mode)
    {
        st_Cooling.b_PowerOnPullDownState = Get_TempPullDownState();
        SimpleFsm_Init(&st_CoolingFsm, CoolingState_CompOff, NULL);
    }
    else
    {
        if(room_range >= RT_UP40 &&
           comp_freq == FREQ_0HZ &&
           eMode_DefrostingCompleted == mode)
        {
            SimpleFsm_Init(&st_CoolingFsm, CoolingState_CompOff, NULL);
        }
        else
        {
            SimpleFsm_Init(&st_CoolingFsm, CoolingState_CompOn, NULL);
        }
    }
    Start_PollTimer(COOLING_CYCLE_POLL_SECONDS);
}

void CoolingCycle_Exit(void)
{
    Stop_PollTimer();
}

CoolingCompState_t Get_CoolingCompState(void)
{
    return (coolingCompState);
}

CoolingCapacityState_t Get_CoolingCapacityState(void)
{
    return (coolingCapacityState);
}

void Clear_CompStillOnTimeMinute(void)
{
    u16_CoolingOnTimerCounter = 0;
    st_Cooling.u16_CompStillOnTimeStart = Get_MinuteCount();
    st_Cooling.u16_CompStillOnTimeMinute = 0;
}

void Clear_CompTotalOnTimeMinute(void)
{
    u16_CoolingOnTimerCounter = 0;
    st_Cooling.u16_CompTotalOnTimeSuspend = 0;
    st_Cooling.u16_CompTotalOnTimeMinute = 0;
}

uint16_t Get_CompStillOnTimeMinute(void)
{
    return (st_Cooling.u16_CompStillOnTimeMinute);
}

uint16_t Get_CompTotalOnTimeMinute(void)
{
    return (st_Cooling.u16_CompTotalOnTimeMinute);
}

uint16_t Get_CompOffTimeSecond(void)
{
    if(u16_CoolingProtectTimerCounter > 0)
    {
        return u16_CoolingProtectTimerCounter;
    }
    return (u16_CoolingOffTimerCounter);
}

void Set_TurboCoolState(bool state)
{
    st_Cooling.b_TurboCool = state;
}

void Set_TurboFreezeState(bool state)
{
    st_Cooling.b_TurboFreeze = state;
}

void Set_DeepFreezeState(bool state)
{
    st_Cooling.b_DeepFreeze = state;
}

bool Get_TurboCoolState(void)
{
    return (st_Cooling.b_TurboCool);
}

bool Get_TurboFreezeState(void)
{
    return (st_Cooling.b_TurboFreeze);
}

bool Get_DeepFreezeState(void)
{
    return (st_Cooling.b_DeepFreeze);
}

CoolingEntryMode_t Get_CoolingEntryMode(void)
{
    return (st_Cooling.coolingEntryMode);
}

uint16_t Get_CoolingCycleNumber(void)
{
    return (st_Cooling.u16_CoolingCycleNumber);
}

bool Get_PowerOnPullDownState(void)
{
    return (st_Cooling.b_PowerOnPullDownState);
}
