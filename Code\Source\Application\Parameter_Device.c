/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Parameter_Device.h"
#include "ParameterManager.h"
#include "Parameter_TemperatureZone.h"
#include "CoolingCycle.h"
#include "FridgeRunner.h"
#include "Driver_Fan.h"
#include "Driver_Flash.h"
#include "Sbus_IceMaker.h"
#include "FactoryMode.h"
#include "CloudControl.h"

#define U8_DOWN_LOAD_TIME_RANGE_NUMBER ((uint8_t)10)

static CoolingOutputState_t coolingOutputState;
static uint8_t u8_FrzFanAdjust;
static uint8_t u8_RefFanAdjust;
static uint8_t u8_CoolFanAdjust;

// 正常压机频率
static const uint8_t ary_NormalCompRevIndex[][U8_FRZ_LEVEL_LENGTH] = {
    //   -24,      -23,          -22,       -21,         -20,       -19,        -18,        -17,        -16    // COMP ON TIME(MINUTE)
    { FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_44HZ, FREQ_44HZ, FREQ_44HZ, FREQ_44HZ }, // RT <=  13
    { FREQ_76HZ, FREQ_76HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_44HZ, FREQ_44HZ }, // 13 < RT <= 18
    { FREQ_76HZ, FREQ_76HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_44HZ, FREQ_44HZ }, // 18 < RT <= 23
    { FREQ_118HZ, FREQ_108HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_54HZ, FREQ_44HZ }, // 23 < RT <= 28
    { FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ, FREQ_76HZ, FREQ_76HZ, FREQ_76HZ, FREQ_76HZ, FREQ_76HZ }, // 28 < RT <= 35
    { FREQ_132HZ, FREQ_132HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ }, // 35 < RT <= 40
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_132HZ, FREQ_132HZ }, // RT > 40
};

// 高负载压机频率
static const uint8_t ary_HighLoadCompRevIndex[][U8_FRZ_LEVEL_LENGTH] = {
    //   -24,      -23,          -22,       -21,         -20,       -19,        -18,        -17,        -16    // COMP ON TIME(MINUTE)
    { FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_76HZ, FREQ_76HZ, FREQ_76HZ, FREQ_76HZ, FREQ_76HZ }, // RT <=  13
    { FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_76HZ, FREQ_76HZ }, // 13 < RT <= 18
    { FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ }, // 18 < RT <= 23
    { FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ }, // 23 < RT <= 28
    { FREQ_132HZ, FREQ_132HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ }, // 28 < RT <= 35
    { FREQ_132HZ, FREQ_132HZ, FREQ_132HZ, FREQ_132HZ, FREQ_132HZ, FREQ_132HZ, FREQ_132HZ, FREQ_118HZ, FREQ_118HZ }, // 35 < RT <= 40
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_132HZ, FREQ_132HZ }, // RT > 40
};

// 快速运转压机频率
static const uint8_t ary_FastRunCompRevIndex[][U8_FRZ_LEVEL_LENGTH] = {
    //   -24,      -23,          -22,       -21,         -20,       -19,        -18,        -17,        -16    // COMP ON TIME(MINUTE)
    { FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ }, // RT <=  13
    { FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ }, // 13 < RT <= 18
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_118HZ, FREQ_118HZ }, // 18 < RT <= 23
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ }, // 23 < RT <= 28
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ }, // 28 < RT <= 35
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ }, // 35 < RT <= 40
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ }, // RT > 40
};

static const uint8_t ary_CompOffFrzFanDuty[(uint8_t)RT_MAXSIZE] = {
    59, // 9V
    59, // 9V
    59, // 9V
    69, // 10V
    69, // 10V
    77, // 11V
    77  // 11V
};

static const uint8_t ary_CompOnFrzFanDuty[FREQ_MAX] = {
    eFanDutyLevel_Off,//0
    eFanDutyLevel_R0, //50 8V
    eFanDutyLevel_R0, //50 8V
    eFanDutyLevel_R0, //50 8V
    eFanDutyLevel_R0, //50 8V
    eFanDutyLevel_R0, //50 8V
    eFanDutyLevel_R1, //59 9V
    eFanDutyLevel_R1, //59 9V
    eFanDutyLevel_R2, //69 10V
    eFanDutyLevel_R2, //69 10V
    eFanDutyLevel_R3, //77 11V
    eFanDutyLevel_R3, //77 11V
};

static const uint8_t ary_CompOnRefFanDuty[FREQ_MAX] = {
    eFanDutyLevel_Off,//0
    eFanDutyLevel_R1, //50 8V
    eFanDutyLevel_R1, //50 8V
    eFanDutyLevel_R0, //30 6V
    eFanDutyLevel_R0, //30 6V
    eFanDutyLevel_R0, //30 6V
    eFanDutyLevel_R1, //50 8V
    eFanDutyLevel_R2, //59 9V
    eFanDutyLevel_R3, //69 10V
    eFanDutyLevel_R4, //77 11V
    eFanDutyLevel_R4, //77 11V
    eFanDutyLevel_R4, //77 11V
};

static const uint8_t ary_CondFanDuty[FREQ_MAX] = {
    eFanDutyLevel_Off,//0
    eFanDutyLevel_R1, //40 FREQ_31HZ
    eFanDutyLevel_R1, //40 FREQ_41HZ
    eFanDutyLevel_R0, //30 FREQ_44HZ
    eFanDutyLevel_R0, //30 FREQ_51HZ
    eFanDutyLevel_R0, //30 FREQ_54HZ
    eFanDutyLevel_R1, //40 FREQ_61HZ
    eFanDutyLevel_R2, //45 FREQ_76HZ
    eFanDutyLevel_R2, //45 FREQ_108HZ
    eFanDutyLevel_R3, //50 FREQ_118HZ
    eFanDutyLevel_R4, //55 FREQ_132HZ
    eFanDutyLevel_R4  //55 FREQ_147HZ
};

static const uint8_t ary_FanDuty[eFanDutyID_Max][eFanDutyLevel_MAX] = {
   {0, 30, 50, 59, 69, 77},//eFanDutyID_RefFan
   {0, 50, 59, 69, 77, 77},//eFanDutyID_FrzFan
   {0, 30, 40, 45, 50, 55},//eFanDutyID_CondFan
};

uint8_t Get_CompFreqIndex(CoolingCapacityState_t state, uint16_t compOnMinute)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t frz_temp_index = 0;
    uint8_t frz_temp_set = Get_FrzSetTemp();
    uint8_t u8_index = 0;
    uint8_t freq_index;
    uint8_t normal_freq_index;
    bool pull_down_state = false;
    bool turbo_freeze = false;
    bool cool_fan_error = Is_FanError(COOL_FAN);
    bool cooling_first = false;
    uint8_t icemaker_mode = eIceMaker_Stop_Mode;
    uint8_t mtype = GetMachineType();
    uint8_t linyun_power = Get_LinYunPowerSave();
    uint8_t mute_enable;
    uint8_t mute_mode;
    static CoolingCapacityState_t freeze_state = eCoolingCapacity_Normal;
    static uint8_t freeze_freq = FREQ_0HZ;

    GetSysParam(SYSPARAM_LINGYUN_MUTE, &mute_enable);
    mute_mode = Get_Mute_Mode();

    if(frz_temp_set < U8_FRZ_ON_OFFLEVEL_MIN)
    {
        frz_temp_index = 0;
    }
    else if(frz_temp_set > U8_FRZ_LEVEL_MAX)
    {
        frz_temp_index = U8_FRZ_LEVEL_MAX - 6;
    }
    else
    {
        frz_temp_index = frz_temp_set - 6;
    }

    switch(state)
    {
        case(CoolingCapacityState_t)eCoolingCapacity_Normal:
            if(linyun_power != 0)
            {
                if(Get_CompFreqStayMinutes() > U8_IMPROVE_FREQ_TIME_MINUTE)
                {
                    freq_index = ary_HighLoadCompRevIndex[room_range][frz_temp_index];
                    coolingOutputState = eOutput_HighLoad;
                    if(IsLinYunTempBelowOn())
                    {
                        Clear_CompFreqStayMinutes();
                    }
                }
                else
                {
                    freq_index = ary_NormalCompRevIndex[room_range][frz_temp_index];
                    coolingOutputState = eOutput_Normal;   
                }
            }
            else if(compOnMinute > U8_IMPROVE_FREQ_TIME_MINUTE)
            {
                freq_index = ary_HighLoadCompRevIndex[room_range][frz_temp_index];
                coolingOutputState = eOutput_HighLoad;
            }
            else
            {
                freq_index = ary_NormalCompRevIndex[room_range][frz_temp_index];
                coolingOutputState = eOutput_Normal;
            }
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_FridgePowerOn:
            pull_down_state = Get_PowerOnPullDownState();
            turbo_freeze = (Get_TurboFreezeState() || Get_DeepFreezeState());
            GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);

            if(true == pull_down_state)
            {
                if(compOnMinute < 60)
                {
                    freq_index = FREQ_76HZ;
                    if(room_range > RT_BELOW28)
                    {
                        freq_index = FREQ_108HZ;
                    }
                }
                else if(compOnMinute < 120)
                {
                    freq_index = FREQ_108HZ;
                    if(room_range > RT_BELOW28)
                    {
                        freq_index = FREQ_118HZ;
                    }
                }
                else
                {
                    freq_index = FREQ_132HZ;
                }
            }
            else
            {
                if(compOnMinute < 120)
                {
                    freq_index = FREQ_54HZ;
                }
                else if(compOnMinute < 240)
                {
                    freq_index = FREQ_108HZ;
                }
                else
                {
                    freq_index = FREQ_132HZ;
                }
            }

            if(true == turbo_freeze || eIceMaker_Quick_Mode == icemaker_mode)
            {
                freq_index = FREQ_118HZ;
            }
            coolingOutputState = eOutput_FridgePowerOn;
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_HighLoad:
            if(compOnMinute > U8_IMPROVE_FREQ_TIME_MINUTE)
            {
                freq_index = ary_FastRunCompRevIndex[room_range][frz_temp_index];
                coolingOutputState = eOutput_FastRunning;
            }
            else
            {
                freq_index = ary_HighLoadCompRevIndex[room_range][frz_temp_index];
                coolingOutputState = eOutput_HighLoad;
            }
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_FastRunning:
            freq_index = ary_FastRunCompRevIndex[room_range][frz_temp_index];
            coolingOutputState = eOutput_FastRunning;
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_EnergyMode:
            normal_freq_index = ary_NormalCompRevIndex[room_range][frz_temp_index];
            if(0 == Get_CoolingCycleNumber())
            {
                cooling_first = true;
            }

            if(RT_BELOW18 == room_range)
            {
                if(compOnMinute < 120 || cooling_first)
                {
                    freq_index = FREQ_31HZ;
                }
                else if(compOnMinute < 240)
                {
                    freq_index = FREQ_41HZ;
                }
                else
                {
                    freq_index = FREQ_61HZ;
                }
            }
            else
            {
                if(compOnMinute < 120 || cooling_first)
                {
                    freq_index = FREQ_54HZ;
                }
                else if(compOnMinute < 240)
                {
                    freq_index = FREQ_54HZ;
                    if(mtype == MACHINE_TYPE_FRENCH)
                    {
                        freq_index = FREQ_61HZ;
                    }
                }
                else
                {
                    freq_index = FREQ_76HZ;
                }
            }

            if(freq_index > normal_freq_index)
            {
                freq_index = normal_freq_index;
            }

            coolingOutputState = eOutput_EnergyMode;
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_FactoryCompleted:
            freq_index = FREQ_108HZ;
            coolingOutputState = eOutput_FactoryCompleted;
            break;
        default:
            break;
    }
    
    if(Is_SmartGrid_DelayLoadOn())
    {
        if(freeze_state != state)
        {
            freeze_freq = freq_index;
        }
        else if(freeze_freq == FREQ_0HZ)
        {
            freeze_freq = freq_index;
        }
        else
        {
            freq_index = freeze_freq;
        }
    }
    else
    {
        freeze_freq = FREQ_0HZ;
    }

    if(mute_enable > 0 &&
       mute_mode > LINYUN_MUTE_LOCAL &&
       (state == eCoolingCapacity_Normal || state == eCoolingCapacity_HighLoad))
    {
        freq_index = ary_NormalCompRevIndex[room_range][frz_temp_index];
        if(mute_mode == LINYUN_MUTE_DEEP)
        {
            freq_index = ary_NormalCompRevIndex[room_range][U8_FRZ_LEVEL_MAX - 6];
        }
    }

    if(true == Get_Strong_Mute())
    {
        if(freq_index <= FREQ_76HZ && freq_index > FREQ_44HZ)
        {
            freq_index = FREQ_44HZ;
        }
        else if(freq_index > FREQ_76HZ && freq_index <= FREQ_108HZ)
        {
            freq_index = FREQ_76HZ;
        }
        else if(freq_index > FREQ_108HZ)
        {
            freq_index = FREQ_108HZ;
        }
    }

    if(true == cool_fan_error)
    {
        if(freq_index > FREQ_76HZ)
        {
            freq_index = FREQ_76HZ;
        }
    }

    if(Is_SmartGrid_ReduceLoadOn())
    {
        freq_index = FREQ_44HZ;
    }

    return (freq_index);
}

uint8_t Get_CompOffFanSettingIndex(RoomTempRange_t range)
{
    linyun_power_param_st *lyparam = Get_LinYunPowerParam();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    uint8_t linyun_power = Get_LinYunPowerSave();
    uint8_t frz_temp_set = Get_FrzSetTemp();
    uint8_t frz_temp_index = 0;
    uint8_t linyun_mute = 0; 
    uint8_t mute_mode = 0;
    uint8_t freq_index;
    uint8_t fan_duty;
    uint8_t level;
    GetSysParam(SYSPARAM_LINGYUN_MUTE, &linyun_mute);
    mute_mode = Get_Mute_Mode();
    if(true == Get_Strong_Mute() || 
       (linyun_mute > 0 && LINYUN_MUTE_DEEP == mute_mode))
    {
        return STRONG_MUTE_FRZ_FAN_DUTY;
    }

    if(linyun_power != 0 && lyparam->frzfan > 0)
    {
        return lyparam->frzfan;
    }

    if(frz_temp_set < U8_FRZ_ON_OFFLEVEL_MIN)
    {
        frz_temp_index = 0;
    }
    else if(frz_temp_set > U8_FRZ_LEVEL_MAX)
    {
        frz_temp_index = U8_FRZ_LEVEL_MAX - 6;
    }
    else
    {
        frz_temp_index = frz_temp_set - 6;
    }

    freq_index = ary_NormalCompRevIndex[range][frz_temp_index];
    level = ary_CompOnFrzFanDuty[freq_index];
    if(IsVarFanNeedAdvance() && level < eFanDutyLevel_R4)
    {
        level++;
    }
    fan_duty = ary_FanDuty[eFanDutyID_FrzFan][level];
    if(b_energy_mode == true && range == RT_BELOW18)
    {
        fan_duty = 50;
    }
    else if(b_energy_mode == true && range == RT_BELOW35)
    {
        fan_duty = 59;
    }

    return (fan_duty);
}

uint8_t Get_CondFanSettingIndex(uint8_t freqIndex)
{
    uint8_t level;
    uint8_t fan_duty_index = 0;
    uint16_t fct_room_temp = Get_FactoryRoomTemp();
    uint16_t run_time = Get_CompStillOnTimeMinute();
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    CoolingCapacityState_t state = Get_CoolingCapacityState();

    if(((CoolingCapacityState_t)eCoolingCapacity_FactoryCompleted == state) &&
        (run_time <= U8_FACTORY_COMPLETED_RUN_TIME) &&
        (fct_room_temp <= CON_32P0_DEGREE)
        )
    {
        fan_duty_index = 0;
    }
    else if(room_range >= (RoomTempRange_t)RT_BELOW18)
    {
        if(0 == freqIndex)
        {
            fan_duty_index = 0;
        }
        else if(0 != u8_CoolFanAdjust)
        {
            fan_duty_index = u8_CoolFanAdjust == MAX_UINT8 ? 0 : u8_CoolFanAdjust;
        }
        else
        {
            level = ary_CondFanDuty[freqIndex];
            if(IsCondFanNeedAdvance() && level < eFanDutyLevel_R4)
            {
                level++;
            }
            fan_duty_index = ary_FanDuty[eFanDutyID_CondFan][level];
            if(b_energy_mode == true)
            {
                fan_duty_index = 55;
            }
        }
    }
    return (fan_duty_index);
}

uint8_t Get_CompOnFrzFanSettingIndex(uint8_t freqIndex)
{
    uint8_t level;
    uint8_t fan_duty = 0;
    uint8_t mute_mode = 0;
    uint8_t linyun_mute = 0; 
    uint8_t linyun_power = Get_LinYunPowerSave();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    RoomTempRange_t room_range = Get_RoomTempRange();
    linyun_power_param_st *lyparam = Get_LinYunPowerParam();

    GetSysParam(SYSPARAM_LINGYUN_MUTE, &linyun_mute);
    mute_mode = Get_Mute_Mode();
    if(true == Get_Strong_Mute() ||
       (linyun_mute > 0 && LINYUN_MUTE_DEEP == mute_mode))
    {
        return STRONG_MUTE_FRZ_FAN_DUTY;
    }

    if(linyun_power != 0 && lyparam->frzfan > 0)
    {
        return lyparam->frzfan;
    }

    if(0 == freqIndex)
    {
        fan_duty = 0;
    }
    else if(0 != u8_FrzFanAdjust)
    {
        fan_duty = u8_FrzFanAdjust == MAX_UINT8 ? 0 : u8_FrzFanAdjust;
    }
    else
    {
        level = ary_CompOnFrzFanDuty[freqIndex];
        if(IsFrzFanNeedAdvance() && level < eFanDutyLevel_R4)
        {
            level++;
        }
        fan_duty = ary_FanDuty[eFanDutyID_FrzFan][level];
        if(b_energy_mode == true && room_range == RT_BELOW18)
        {
            fan_duty = 50;
        }
        else if(b_energy_mode == true && room_range == RT_BELOW35)
        {
            fan_duty = 59;
        }
    }
    return (fan_duty);	
}

uint8_t Get_CompOnRefFanSettingIndex(uint8_t freqIndex)
{
    uint8_t level;
    uint8_t fan_duty = 0;
    uint8_t mute_mode = 0;
    uint8_t linyun_mute = 0; 
    uint8_t linyun_power = Get_LinYunPowerSave();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    RoomTempRange_t room_range = Get_RoomTempRange();
    linyun_power_param_st *lyparam = Get_LinYunPowerParam();

    GetSysParam(SYSPARAM_LINGYUN_MUTE, &linyun_mute);
    mute_mode = Get_Mute_Mode();
    if(true == Get_Strong_Mute() ||
       (linyun_mute > 0 && LINYUN_MUTE_DEEP == mute_mode))
    {
        return STRONG_MUTE_REF_FAN_DUTY;
    }

    if(linyun_power != 0 && lyparam->reffan)
    {
        return lyparam->reffan;
    }

    if(0 == freqIndex)
    {
        fan_duty = 0;
    }
    else if(0 != u8_RefFanAdjust)
    {
        fan_duty = u8_RefFanAdjust;
    }
    else
    {
        level = ary_CompOnRefFanDuty[freqIndex];
        if(IsRefFanNeedAdvance() && level < eFanDutyLevel_R4)
        {
            level++;
        }
        fan_duty = ary_FanDuty[eFanDutyID_RefFan][level];
        if(b_energy_mode == true)
        {
            fan_duty = 50;
        }
    }
    return (fan_duty);
}


CoolingOutputState_t Get_CoolingOutputState(void)
{
    return (coolingOutputState);
}

void Set_FrzFanAdjustParm(uint8_t adjust_value)
{
    u8_FrzFanAdjust = adjust_value;
}

uint16_t Get_FrzFanAdjustParm(void)
{
    return (u8_FrzFanAdjust);
}

void Set_CoolFanAdjustParm(uint8_t adjust_value)
{
    u8_CoolFanAdjust = adjust_value;
}

uint16_t Get_CoolFanAdjustParm(void)
{
    return (u8_CoolFanAdjust);
}

void Set_RefFanAdjustParm(uint8_t adjust_value)
{
    u8_RefFanAdjust = adjust_value;
}

uint16_t Get_RefFanAdjustParm(void)
{
    return (u8_RefFanAdjust);
}

