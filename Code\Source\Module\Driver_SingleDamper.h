/*!
 * @file
 * @brief This file defines public constants, types and functions for the single damper.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _DRIVER_SINGLEDAMPER_H_
#define _DRIVER_SINGLEDAMPER_H_

#include <stdint.h>
#include <stdbool.h>

#define Con_SingleDamperOpenSteps ((uint16_t)1750)
#define Con_SingleDamperCloseSteps ((uint16_t)1850)
#define Con_SingleDamperCheckPartSteps ((uint16_t)65535)

#define Con_SingleDamperProtectTimeSecond ((uint8_t)60)

#define Con_SingleDamperRotateTimeMinute ((uint8_t)60)

#define Con_SingleDamperTempResetTimeSecond ((uint16_t)2400) // 40min

#define Con_SingleDamperOpenDirection ((uint8_t)1)
#define Con_SingleDamperCloseDirection ((uint8_t)0)

#define Con_SingleDamperRunPPSTimeMs ((uint8_t)4)
#define Con_SingleDamperStartDelayTimeSecond ((uint8_t)2)

enum
{
    Damper_ID0,
    Damper_MaxNumber
};

enum
{
    Damper_Invaild = -1,
    Damper_Ref = Damper_Invaild,
    Damper_Var = Damper_ID0,
    DamperIDMax
};
typedef uint8_t DamperID_t;

enum
{
    DAMPER_AllClose,
    DAMPER_Angle15Open,
    DAMPER_Angle30Open,
    DAMPER_Angle45Open,
    DAMPER_Angle60Open,
    DAMPER_Angle75Open,
    DAMPER_AllOpen,
    DAMPER_MaxState,
    DAMPER_FREEZED
};
typedef uint8_t DamperState_t;

enum
{
    DAMPER_STEP_STATE_A,
    DAMPER_STEP_STATE_B,
    DAMPER_STEP_STATE_C,
    DAMPER_STEP_STATE_D,
    DAMPER_STEP_STATE_MAX
};
typedef uint8_t DamperStepState_t;

typedef struct
{
    bool b_IsDamperInitialized;
    bool b_IsDamperRunning;
    bool b_IsDamperFreeze;
    bool b_AlreadyRest;
    bool b_NeedToResetDamper;
    uint8_t u8_DamperDirection;
    uint8_t u8_DamperPPSTimer;
    uint8_t u8_DamperIdleTimer;
    DamperState_t damperNowState;
    DamperState_t damperMidState;
    DamperState_t damperNewState;
    DamperStepState_t damperStepState;
    uint16_t u16_DamperSteps;
    uint16_t u16_DamperNoActionSecond;
    DamperID_t id;
    void (*p_SetDamperIO)(bool b_IO_En, bool b_IO_InA, bool b_IO_InB);
} SingleDamperDriver_st;

void Init_SingleDamper(void);
void Execute_SingleDamperDriver(void);
void Set_SingleDamperState(SingleDamperDriver_st *p_DamperInst, DamperState_t settingState);
void Reset_SingleDamper(DamperID_t damperID);
void ForcedCtrl_SingleDamper(DamperID_t damperID, bool b_IsDamperForcedRunning);
void Handle_SingleDamperTimer(void);
void RefDamper_Init(void);
void Set_RefDamperState(DamperState_t settingState);
void Set_VarDamperState(DamperState_t settingState);
bool Get_RefDamperState(void);
bool Get_VarDamperState(void);
bool IsSingleDamperAlreadyReset(DamperID_t damperID);
void Freeze_SingleDamper(DamperID_t damperID, bool enable);
#endif
