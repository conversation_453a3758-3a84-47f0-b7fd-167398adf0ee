

#ifndef DRIVE_VALVE_H
#define DRIVE_VALVE_H

/******************************************************************************
INCLIIDE 头文件包含
******************************************************************************/
#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "Adpt_GPIO.h"

/******************************************************************************
MACRO DEFINITION 宏定义
******************************************************************************/

// 电动阀类型定义
#define VALVE_THREEWAY_200STEPS         0       // 三通200步
#define VALVE_THREEWAY_48STEPS          1       // 三通48步
#define VALVE_FOURWAY_48STEPS           2       // 四通48步
#define VALVE_FOURWAY_400STEPS          3       // 四通400步

#define VALVE_TYPE VALVE_THREEWAY_200STEPS

extern uint8_t u8_unused;

// 电动阀驱动端口定义
#define IO_Valve_EN      u8_unused // 低电平有效

/******************************************************************************
VARIABLES TYPE DEFINE 变量类型声明
******************************************************************************/

#if(VALVE_THREEWAY_200STEPS == VALVE_TYPE)

// 阀位置
typedef enum
{
    Valve_Initial = 0,
    Valve_AllClose,
    Valve_FrzON_RefOFF,
    Valve_AllOpen,
    Valve_FrzOFF_RefON,
    MAX_ValveState
} ValveState_em;

#elif(VALVE_THREEWAY_48STEPS == VALVE_TYPE)

// 阀位置
typedef enum
{
    Valve_Initial = 0,
    Valve_FrzOFF_RefON,
    Valve_FrzON_RefOFF,
    Valve_AllOpen,
    Valve_AllClose,
    MAX_ValveState
} ValveState_em;

#elif(VALVE_FOURWAY_48STEPS == VALVE_TYPE)

// 阀位置
typedef enum
{
    Valve_Initial = 0,
    Valve_RefON_FrzOFF_VarON,
    Valve_RefOFF_FrzON_VarOFF,
    Valve_RefOFF_FrzOFF_VarON,
    Valve_RefOFF_FrzOFF_VarOFF,
    Valve_RefON_FrzOFF_VarOFF,
    MAX_ValveState
} ValveState_em;

#elif(VALVE_FOURWAY_400STEPS == VALVE_TYPE)

// 阀位置
typedef enum
{
    Valve_Initial = 0,
    Valve_RefOFF_FrzOFF_VarOFF,
    Valve_RefOFF_FrzOFF_VarOFF,
    Valve_RefON_FrzOFF_VarOFF,
    Valve_RefOFF_FrzON_VarOFF,
    Valve_RefOFF_FrzOFF_VarON,
    MAX_ValveState
} ValveState_em;

#endif

// 电动阀驱动参数
typedef struct
{
    uint16_t u16_RunSteps;
    uint16_t u16_PPSTimer;
    uint16_t u16_RestTimerMinutes;
    uint32_t u32_StayTimerMsecCount[MAX_ValveState];

    uint8_t u8_CycleSteps;

    ValveState_em em_ValveNowState;
    ValveState_em em_ValveNewState;
    ValveState_em em_ValveTargetState;

    bool b_CtrlInit;               // 电动阀控制初始化
    bool b_ForceReset;             // 电动阀强制复位
    bool b_Running;                // 电动阀旋转中
    bool b_Direction;              // 电动阀旋转方向
    bool b_FirstStep;              // 电动阀旋转第一步
    bool b_NeedReset;
    bool b_AlreadyRest;
} DriveValve_st;

/******************************************************************************
GLOBAL VARIABLES 全局变量声明
******************************************************************************/
extern DriveValve_st st_DriveValve;

/******************************************************************************
GLOBAL FUNCTIONS  全局函数声明
******************************************************************************/

// 电动阀驱动控制初始化
extern void Drive_ValveInit(void);

// 电动阀驱动强制运行或停止，做电用
extern void Drive_ValveForce(bool b_State);

// 电动阀复位
extern void Drive_ValveReset(void);

// 电动阀设置位置
extern void Drive_ValveSetState(ValveState_em em_ValveState);

// 电动阀旋转
extern void Drive_ValveRun(void);

//判断电动阀复位周期是否到期
extern bool Driver_ValveNeedReset(void);

//获取电动阀状态持续时间(分钟)
extern uint32_t Driver_ValveStateGetStayTime(ValveState_em state);

extern void Driver_ValveStateClearStayTime(void);

extern bool IsValveAlreadyReset(void);

extern uint8_t Get_ValveState(void);

extern void Driver_ValveForceNeedReset(void);

/******************************************************************************
******************************************************************************/

#endif
