/*!
 * @file
 * @brief Manages all the state variables of the system.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "DisplayInterface.h"
#include "Core_CallBackTimer.h"
#include "Parameter_TemperatureZone.h"
#include "CoolingCycle.h"
#include "SystemTimerModule.h"
#include "FridgeRunner.h"
#include "Driver_Flash.h"
#include "Sbus_IceMaker.h"

#define U16_UI_POLL_MILLISECONDS (uint16_t)100
#define U16_UI_STARTUP_TIME_100MSEC_COUNT (uint16_t)30 // 3s
#define U16_UI_ACTIVE_TIME_100MSEC_COUNT (uint16_t)250 // 25s
#define U16_TURBO_COOL_MINUTES (uint16_t)360 // 6h
#define U16_TURBO_FREEZE_MINUTES (uint16_t)3000 // 50h
#define U16_DEEP_FREEZE_MINUTES (uint16_t)3000 // 50h
#define U16_FAST_ICEMAKER_MINUTES (uint16_t)360 // 6h

static UserMode_t userMode;
static bool refDisbale;
static uint16_t u16_TurboCoolTimerStart;
static uint16_t u16_TurboFreezeTimerStart;
static uint16_t u16_DeepFreezeTimerStart;
static uint16_t u16_FastIceMakerTimerStart;
static uint16_t u16_TurboCoolTimer = 0;
static uint16_t u16_TurboFreezeTimer = 0;
static uint16_t u16_DeepFreezeTimer = 0;
static uint16_t u16_FastIceMakerTimer = 0;
static bool b_FastIceMakerFrzFanRun = false;

void Process_UserMode(void)
{
    uint8_t val;
    RunningState_t run_state;
    uint8_t icemaker_mode = eIceMaker_Stop_Mode;
    CoolingCompState_t comp_state = Get_CoolingCompState();
    
    run_state = Get_RunningState();
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);
    if((UserMode_t)eTurboCool_Mode == userMode && run_state == eRunning_CoolingCycle)
    {
        u16_TurboCoolTimer = Get_MinuteElapsedTime(u16_TurboCoolTimerStart);
        if(u16_TurboCoolTimer >= U16_TURBO_COOL_MINUTES)
        {
            GetSysParam(SYSPARAM_REFTEMP, &val);
            Update_RefSetTemp(val);
            userMode = (UserMode_t)eManual_Mode;
            SetSysParam(SYSPARAM_USER_MODE, userMode);
            Set_TurboCoolState(false);
        }
        else
        {
            Set_TurboCoolState(true);
        }
    }
    else
    {
        Set_TurboCoolState(false);
        u16_TurboCoolTimerStart = Get_MinuteCount();
        u16_TurboCoolTimer = 0;
    }

    if((UserMode_t)eTurboFreeze_Mode == userMode)
    {
        u16_TurboFreezeTimer = Get_MinuteElapsedTime(u16_TurboFreezeTimerStart);
        if(u16_TurboFreezeTimer >= U16_TURBO_FREEZE_MINUTES)
        {
            GetSysParam(SYSPARAM_FRZTEMP, &val);
            Update_FrzSetTemp(val);
            userMode = (UserMode_t)eManual_Mode;
            SetSysParam(SYSPARAM_USER_MODE, userMode);
            Set_TurboFreezeState(false);
            Set_TurboFreezeDefrostingAutoExitState(true);
        }
        else
        {
            Set_TurboFreezeState(true);
        }
    }
    else
    {
        Set_TurboFreezeState(false);
        u16_TurboFreezeTimerStart = Get_MinuteCount();
        u16_TurboFreezeTimer = 0;
    }

    if((UserMode_t)eDeepFreeze_Mode == userMode)
    {
        u16_DeepFreezeTimer = Get_MinuteElapsedTime(u16_DeepFreezeTimerStart);
        if(u16_DeepFreezeTimer >= U16_DEEP_FREEZE_MINUTES)
        {
            GetSysParam(SYSPARAM_FRZTEMP, &val);
            Update_FrzSetTemp(val);
            userMode = (UserMode_t)eManual_Mode;
            SetSysParam(SYSPARAM_USER_MODE, userMode);
            Set_DeepFreezeState(false);
            Set_TurboFreezeDefrostingAutoExitState(true);
        }
        else
        {
            Set_DeepFreezeState(true);
        }
    }
    else
    {
        Set_DeepFreezeState(false);
        u16_DeepFreezeTimerStart = Get_MinuteCount();
        u16_DeepFreezeTimer = 0;
    }

    if(icemaker_mode == eIceMaker_Quick_Mode)
    {
        b_FastIceMakerFrzFanRun = false;
        u16_FastIceMakerTimer = Get_MinuteElapsedTime(u16_FastIceMakerTimerStart);
        if(u16_FastIceMakerTimer >= U16_FAST_ICEMAKER_MINUTES)
        {
            icemaker_mode = eIceMaker_Normal_Mode;
            SetSysParam(SYSPARAM_ICEMAKER_FUNC, icemaker_mode);
        }
    }
    else
    {
        u16_FastIceMakerTimerStart = Get_MinuteCount();
        if(u16_FastIceMakerTimer > 0 &&
           run_state == eRunning_CoolingCycle &&
           comp_state <= eCooling_CompProtect)
        {
            b_FastIceMakerFrzFanRun = true;
        }
        else
        {
            u16_FastIceMakerTimer = 0;
            b_FastIceMakerFrzFanRun = false;
        }
    }
}

void Update_UserModeTempFrzSet(void)
{
    uint8_t val;
    switch(userMode)
    {
        case(UserMode_t)eFuzzy_Mode:
            Update_FrzSetTempNotSave(FRZ_LEVEL_F18);
            break;
        case(UserMode_t)eTurboCool_Mode:
            GetSysParam(SYSPARAM_FRZTEMP, &val);
            Update_FrzSetTempNotSave(val);
            break;
        case(UserMode_t)eTurboFreeze_Mode:
            Update_FrzSetTempNotSave(FRZ_LEVEL_F24);
            break;
        case(UserMode_t)eManual_Mode:
            GetSysParam(SYSPARAM_FRZTEMP, &val);
            Update_FrzSetTempNotSave(val);
            break;
        case (UserMode_t)eDeepFreeze_Mode:
            Update_FrzSetTempNotSave(FRZ_LEVEL_F30);
            break;
        default:
            break;
    }
}

void Update_UserModeTempRefSet(void)
{
    uint8_t val;
    switch(userMode)
    {
        case(UserMode_t)eFuzzy_Mode:
            Update_RefSetTempNotSave(REF_LEVEL_5);
            break;
        case(UserMode_t)eTurboCool_Mode:
            Update_RefSetTempNotSave(REF_LEVEL_2);
            break;
        case(UserMode_t)eTurboFreeze_Mode:
            GetSysParam(SYSPARAM_REFTEMP, &val);
            Update_RefSetTempNotSave(val);
            break;
        case(UserMode_t)eManual_Mode:
            GetSysParam(SYSPARAM_REFTEMP, &val);
            Update_RefSetTempNotSave(val);;
            break;
        case (UserMode_t)eDeepFreeze_Mode:
            GetSysParam(SYSPARAM_REFTEMP, &val);
            Update_RefSetTempNotSave(val);
            break;
        default:
            break;
    }
}

void Set_UserMode(UserMode_t mode)
{
    uint8_t val;

    if(mode < (UserMode_t)eMax_Mode)
    {
        if(userMode != mode)
        {
            if(userMode == eTurboFreeze_Mode || userMode == eDeepFreeze_Mode)
            {
                Set_TurboFreezeDefrostingAutoExitState(false);
            }

            userMode = mode;
            switch(userMode)
            {
                case(UserMode_t)eFuzzy_Mode:
                    Update_RefSetTemp(REF_LEVEL_5);
                    Update_FrzSetTemp(FRZ_LEVEL_F18);
                    break;
                case(UserMode_t)eTurboCool_Mode:
                    Update_RefSetTemp(REF_LEVEL_2);
                    GetSysParam(SYSPARAM_FRZTEMP, &val);
                    Update_FrzSetTemp(val);
                    break;
                case(UserMode_t)eTurboFreeze_Mode:
                    GetSysParam(SYSPARAM_REFTEMP, &val);
                    Update_RefSetTemp(val);
                    Update_FrzSetTemp(FRZ_LEVEL_F24);
                    break;
                case(UserMode_t)eManual_Mode:
                    GetSysParam(SYSPARAM_REFTEMP, &val);
                    Update_RefSetTemp(val);
                    GetSysParam(SYSPARAM_FRZTEMP, &val);
                    Update_FrzSetTemp(val);
                    break;
                case (UserMode_t)eDeepFreeze_Mode:
                    GetSysParam(SYSPARAM_REFTEMP, &val);
                    Update_RefSetTemp(val);
                    Update_FrzSetTemp(FRZ_LEVEL_F30);
                    break;
                default:
                    break;
            }
        }
        SetSysParam(SYSPARAM_USER_MODE, userMode);
    }
}

void Set_RefDisable(uint8_t disable)
{
    if(disable != 0 && userMode == eTurboCool_Mode)
    {
        Set_UserMode(eManual_Mode);
    }

    if(disable)
    {
        refDisbale = true;
    }
    else
    {
        refDisbale = false;
    }
    SetSysParam(SYSPARAM_REF_DISABLE, disable);
}

bool Get_RefDisable(void)
{
    return refDisbale;
}

uint16_t Get_TurboCoolTimeMinute(void)
{
    return (u16_TurboCoolTimer);
}

uint16_t Get_TurboFreezeTimeMinute(void)
{
    if((UserMode_t)eTurboFreeze_Mode == userMode)
    {
        return (u16_TurboFreezeTimer);
    }
    else if ((UserMode_t)eDeepFreeze_Mode == userMode)
    {
        return (u16_DeepFreezeTimer);
    }
    return 0;
}

UserMode_t Get_UserMode(void)
{
    return (userMode);
}

bool IsFastIceMakerFrzFanNeedRun(void)
{
    return b_FastIceMakerFrzFanRun;
}
