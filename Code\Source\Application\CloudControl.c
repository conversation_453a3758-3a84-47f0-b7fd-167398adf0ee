/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "CoolingCycle.h"
#include "FridgeRunner.h"
#include "CloudControl.h"
#include "Parameter_TemperatureZone.h"
#include "SystemTimerModule.h"
#include "Driver_Flash.h"
#include "DisplayInterface.h"
#include "Sbus_IceMaker.h"
#include "Driver_DoorSwitch.h"
#include "ResolverDevice.h"
#include "Driver_AdSample.h"
#include "Driver_SingleDamper.h"
#include "InverterUsart.h"
#include "Drive_Valve.h"
#include "SystemManager.h"
#include "FaultCode.h"
#include "Iot_Spec.h"
#include "miio_api.h"

static bool b_PeekValleyPowerFrz = false;
static bool b_PeekValleyPowerRef = false;
static uint16_t u16_PeekValleyPowerTimerStart = 0;
static uint16_t u16_PeekValleyPowerTimer = 0;
static uint8_t u8_PeekValleyPowerFrzSet;
static uint8_t u8_PeekValleyPowerRefSet;
static uint8_t u8_LinYunPowerSave;
static uint8_t u8_AiNoiseReduce;
static uint16_t u16_AiNoiseReduceTimerStart = 0;
static uint8_t u8_RefIonEnable = 0;
static uint8_t u8_FrzIonEnable = 0;
static uint16_t u16_RefIonCount;
static uint16_t u16_FrzIonCount;
static uint16_t u16_RefIonCycles;
static uint16_t u16_FrzIonCycles;
static uint16_t u16_RefIonTargetCycles;
static uint16_t u16_FrzIonTargetCycles;
linyun_power_param_st lyparam;
uint16_t u16_LinYunPowerTimerStart = 0;
uint8_t u8_LinYunPowerParamGap = 0;
uint8_t u8_LinYunPowerParamGapCount = 0;
uint8_t u8_LinYunPowerParamReport = 1;
static uint8_t u8_Strong_Cool = 0;
static uint8_t u8_Strong_Mute = 0;
static linyun_mute_mode_e mute_mode = LINYUN_MUTE_LOCAL;
static uint8_t u8_cloud_netstate = 0;
static uint16_t u16_CloudOfflineStartMinute = 0;
static uint16_t u16_CloudOnlineStartMinute = 0;
static uint16_t u16_MuteNormalHighLoadStart = 0;
static uint16_t u16_MuteDeepHighLoadStart = 0;
static uint16_t u16_MuteDeepStart = 0;
static uint16_t u16_LastCompCoolingDuration = 0;
static uint8_t u8_SmartGrid_Forbid_Deforst = 0;
static uint8_t u8_SmartGrid_Deforst = 0;
static uint8_t u8_SmartGrid_DelayLoad = 0;
static uint8_t u8_SmartGrid_DelayLoad_On = 0;
static uint8_t u8_SmartGrid_Forbid_DelayLoadDeforst = 0;
static uint8_t u8_SmartGrid_ReduceLoad = 0;
static uint8_t u8_SmartGrid_ReduceLoad_On = 0;
static uint8_t u8_SmartGrid_Forbid_ReduceLoadDeforst = 0;
static uint16_t u16_SmartGridDelayLoadStart = 0;
static uint16_t u16_SmartGridReduceLoadStart = 0;
int16_t peekValleyPowerRefTemp = -1;
int16_t peekValleyPowerFrzTemp = -1;

static void ActiveCloudNetState(void)
{
    u8_cloud_netstate = 1;
    u16_CloudOfflineStartMinute = Get_MinuteCount();    
}

static void JudgeCloudNetState(void)
{
    static uint8_t poll_cycles = 0;
    static uint8_t u8_cloud_init = 0;
    net_state_e net_state = get_dev_net_state();

    if(u8_cloud_init == 0)
    {
        if(u8_cloud_netstate == 0)
        {
            u16_CloudOnlineStartMinute = Get_MinuteCount();
        }
        u8_cloud_init = 1;
    }

    if(poll_cycles++ < CLOUD_NET_POLL_SECONDS)
    {
        return;
    }

    poll_cycles = 0;

    if(net_state != ZM_APP_NET_STATE_CLOUD)
    {
        if(u8_cloud_netstate == 1 &&
           Get_MinuteElapsedTime(u16_CloudOfflineStartMinute) > CLOUD_NET_OFFLINE_TIME)
        {
            u8_cloud_netstate = 0;
        }
        u16_CloudOnlineStartMinute = Get_MinuteCount();
    }
    else
    {
        if(u8_cloud_netstate == 0 &&
           Get_MinuteElapsedTime(u16_CloudOnlineStartMinute) > CLOUD_NET_ONLINE_TIME)
        {
            u8_cloud_netstate = 1;
        }
        u16_CloudOfflineStartMinute = Get_MinuteCount();
    }      
}

static void JudgeSmartGridState(void)
{
    uint8_t enable = 0;

    GetSysParam(SYSPARAM_SMARTGRID_DEFORST, &enable);
    if(enable > 0 && u8_cloud_netstate > 0)
    {
        u8_SmartGrid_Forbid_Deforst = 1;
    }
    else
    {
        u8_SmartGrid_Forbid_Deforst = 0;
    }

    if(u8_SmartGrid_DelayLoad > 0 && 
       eFridge_Running == Get_FridgeState() &&
       eRunning_CoolingCycle == Get_RunningState() &&
       eEnterState_Forbid == Get_EnterDefrostingState())
    {
        u8_SmartGrid_Forbid_DelayLoadDeforst = 1;
        u8_SmartGrid_DelayLoad_On = 0;
        if(Get_MinuteElapsedTime(u16_SmartGridDelayLoadStart) > SMARTGRID_DELAY_MINUTES)
        {
            u8_SmartGrid_DelayLoad_On = 1;
        }
        
        if(Get_MinuteElapsedTime(u16_SmartGridDelayLoadStart) > (SMARTGRID_DELAY_MINUTES + SMARTGRID_DELAYLOAD_MINUTES))
        {
            u8_SmartGrid_DelayLoad = 0;
            u8_SmartGrid_DelayLoad_On = 0;
            u8_SmartGrid_Forbid_DelayLoadDeforst = 0;
        }

    }
    else
    {
        u16_SmartGridDelayLoadStart = Get_MinuteCount();
        u8_SmartGrid_DelayLoad_On = 0;
        u8_SmartGrid_Forbid_DelayLoadDeforst = 0;
        
    }

    if(u8_SmartGrid_ReduceLoad > 0 && 
       eFridge_Running == Get_FridgeState() &&
       eRunning_CoolingCycle == Get_RunningState() &&
       eEnterState_Forbid == Get_EnterDefrostingState())
    {
        u8_SmartGrid_Forbid_ReduceLoadDeforst = 1;
        u8_SmartGrid_ReduceLoad_On = 0;
        if(Get_MinuteElapsedTime(u16_SmartGridReduceLoadStart) > SMARTGRID_DELAY_MINUTES)
        {
            u8_SmartGrid_ReduceLoad_On = 1;
        }

        if(Get_MinuteElapsedTime(u16_SmartGridReduceLoadStart) > (SMARTGRID_DELAY_MINUTES + SMARTGRID_REDUCELOAD_MINUTES))
        {
            u8_SmartGrid_ReduceLoad = 0;
            u8_SmartGrid_ReduceLoad_On = 0;
            u8_SmartGrid_Forbid_ReduceLoadDeforst = 0;
        }
    }
    else
    {
        u16_SmartGridReduceLoadStart = Get_MinuteCount();
        u8_SmartGrid_ReduceLoad_On = 0;
        u8_SmartGrid_Forbid_ReduceLoadDeforst = 0;
    }
}

static int16_t JudgePeekValleyPowerTempFrzSet(void)
{
    uint8_t mode;
    uint8_t icemaker_mode;
    uint8_t u8_peek_valley_power = 0;
    uint32_t totaltime = Get_FridgePowerOnTimeMinute();
    uint8_t frz_set;

    GetSysParam(SYSPARAM_FRZTEMP, &frz_set);
    GetSysParam(SYSPARAM_PEEK_VALLEY_POWER, &u8_peek_valley_power);
    GetSysParam(SYSPARAM_USER_MODE, &mode);
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);

    if(u8_peek_valley_power == 0 || u8_cloud_netstate == 0)
    {
        Clean_PeekValleyFrzSet();
    }

    if(u8_peek_valley_power == 0
        || b_PeekValleyPowerFrz == false
        || totaltime < U16_PEEK_VALLEY_ON_MINUTES
        || mode == eDeepFreeze_Mode
        || mode == eTurboFreeze_Mode
        || icemaker_mode == eIceMaker_Quick_Mode)
    {
        return -1;
    }

    if(u8_PeekValleyPowerFrzSet < frz_set &&
       (frz_set <= FRZ_LEVEL_F21 || 
        Get_RoomTempRange() > RT_BELOW35))
    {
        return -1;
    }

    if(Get_MinuteElapsedTime(u16_PeekValleyPowerTimerStart) > U16_PEEK_VALLEY_OFF_MINUTES)
    {
        b_PeekValleyPowerFrz = false;
        return -1;
    }

    return u8_PeekValleyPowerFrzSet;
}

static int16_t JudgePeekValleyPowerTempRefSet(void)
{
    uint8_t mode;
    uint8_t icemaker_mode;
    uint8_t u8_peek_valley_power = 0;
    uint32_t totaltime = Get_FridgePowerOnTimeMinute();

    GetSysParam(SYSPARAM_PEEK_VALLEY_POWER, &u8_peek_valley_power);
    GetSysParam(SYSPARAM_USER_MODE, &mode);

    if(u8_peek_valley_power == 0 || u8_cloud_netstate == 0)
    {
        Clean_PeekValleyRefSet();
    }

    if(u8_peek_valley_power == 0 
        || b_PeekValleyPowerRef == false
        || totaltime < U16_PEEK_VALLEY_ON_MINUTES
        || mode == eTurboCool_Mode
        || (Get_RefDisable() == true))
    {
        return -1;
    }

    if(Get_MinuteElapsedTime(u16_PeekValleyPowerTimerStart) > U16_PEEK_VALLEY_OFF_MINUTES)
    {
        b_PeekValleyPowerRef = false;
        return -1;
    }

    return u8_PeekValleyPowerRefSet;
}

static bool IsLinYunPowerNeedReport(void)
{
    uint8_t mode;
    uint8_t powersave;
    uint8_t mute_state;
    uint8_t icemaker_mode;
    static uint16_t retry_report_timer = 0;
    net_state_e net_state = get_dev_net_state();
    bool b_ref_disable = Get_RefDisable();
    UserMode_t user_mode = Get_UserMode();
    RoomTempRange_t rt = Get_RoomTempRange();
    uint8_t fault_number = Get_FaultCodeNumber();
    FridgeState_t device_state = Get_FridgeState();
    RunningState_t run_state = Get_RunningState();
    uint16_t compstillon = Get_CompStillOnTimeMinute();
    uint16_t comptotalon = Get_CompTotalOnTimeMinute();
    bool energy_mode = Get_EnergyConsumptionModeState();
    CoolingCapacityState_t cool_state = Get_CoolingCapacityState();
    
    GetSysParam(SYSPARAM_LINGYUN_POWER, &powersave);
    GetSysParam(SYSPARAM_LINGYUN_MUTE, &mute_state);
    GetSysParam(SYSPARAM_LINGYUN_POWER, &powersave);
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);

    if(powersave > 0 &&
       net_state == ZM_APP_NET_STATE_CLOUD &&
       rt > RT_BELOW13 &&
       device_state == eFridge_Running &&
       run_state == eRunning_CoolingCycle &&
       comptotalon > 0 &&
       cool_state == eCoolingCapacity_Normal &&
       energy_mode == false &&
       u8_Strong_Cool == 0 &&
       u8_Strong_Mute == 0 &&
       mute_state == 0 &&
       fault_number == 0 &&
       b_ref_disable == false &&
       u8_LinYunPowerParamReport > 0
    )
    {
        return true;
    }    

    if(u8_LinYunPowerParamReport > 0 )
    {
        retry_report_timer = Get_MinuteCount();
    }
    else if(Get_MinuteElapsedTime(retry_report_timer) > LINYUN_RETRY_UPLOAD_MINUTES)
    {
        u8_LinYunPowerParamReport = 1;

    }
    return false;
}

static void UploadLinYunPowerSave(void)
{
    static uint8_t upload_timer = 0;

    if(IsLinYunPowerNeedReport() == false)
    {
        upload_timer = 0;
        return;
    }
     
    if(upload_timer++ > LINYUN_POWERPARAM_UPLOAD_CYCLES)
    {
        if(execute_wifi_cmd_async(WIFI_CMD_LINYUNPOWER_UPLOAD, NULL) == 0)
        {
            upload_timer = 0;
        }
        else if(upload_timer > LINYUN_POWERPARAM_UPLOAD_TIMEOUT)
        {
            upload_timer = 0;
        }
    }
}

static void JudgeLinYunPowerSave(void)
{
    uint8_t mode;
    uint8_t powersave;
    uint8_t mute_state;
    bool is_sensor_error;
    uint8_t icemaker_mode;
    bool b_ref_disable = Get_RefDisable();
    UserMode_t user_mode = Get_UserMode();
    RoomTempRange_t rt = Get_RoomTempRange();
    uint8_t fault_number = Get_FaultCodeNumber();
    bool is_ref_forst = Get_RefFrostReduceMode();
    RunningState_t run_state = Get_RunningState();
    FridgeState_t device_state = Get_FridgeState();
    uint16_t compstillon = Get_CompStillOnTimeMinute();
    bool energy_mode = Get_EnergyConsumptionModeState();
    CoolingCapacityState_t cool_state = Get_CoolingCapacityState();
    
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);
    GetSysParam(SYSPARAM_LINGYUN_POWER, &powersave);
    GetSysParam(SYSPARAM_LINGYUN_MUTE, &mute_state);
    
    if(powersave == 0)
    {
        u8_LinYunPowerSave = 0;
        u8_LinYunPowerParamGap = 0;
        u8_LinYunPowerParamGapCount = 0;
        return;
    }

    if(u8_LinYunPowerParamGap)
    {
        if(Get_MinuteElapsedTime(u16_LinYunPowerTimerStart) > LINYUN_POWERPARAM_GAP_MINUTES)
        {
            u8_LinYunPowerParamGap = 0;
            memset(&lyparam, 0, sizeof(linyun_power_param_st));
        }
    }

    if(u8_LinYunPowerSave == 0)
    {
        if(rt > RT_BELOW13 &&
           device_state == eFridge_Running &&
           run_state == eRunning_CoolingCycle &&
           cool_state == eCoolingCapacity_Normal &&
           compstillon > LINYUN_POWER_COMPON_MINUTES &&
           energy_mode == false &&
           u8_Strong_Cool == 0 &&
           u8_Strong_Mute == 0 &&
           mute_state == 0 &&
           fault_number == 0 &&
           b_ref_disable == false &&
           (is_ref_forst == false) &&
           (u8_LinYunPowerParamGap &&
            u8_LinYunPowerParamGapCount > LINYUN_POWERPARAM_GAP_COUNT &&
            lyparam.state > 0))
        {
            u8_LinYunPowerSave = 1;
            u8_LinYunPowerParamGapCount = 0;
        }
    }
    else
    {
        if(rt == RT_BELOW13 ||
           device_state != eFridge_Running ||
           run_state != eRunning_CoolingCycle ||
           cool_state != eCoolingCapacity_Normal ||
           user_mode == eTurboCool_Mode ||
           user_mode == eTurboFreeze_Mode ||
           user_mode == eDeepFreeze_Mode ||
           icemaker_mode == eIceMaker_Quick_Mode ||
           u8_Strong_Cool > 0 ||
           u8_Strong_Mute > 0 ||
           mute_state > 0 ||
           energy_mode == true ||
           fault_number != 0 ||
           b_ref_disable == true ||
           (u8_LinYunPowerParamGap == 0 ||
            u8_LinYunPowerParamGapCount > LINYUN_POWERPARAM_GAP_COUNT ||
            lyparam.state < 0))
        {
            u8_LinYunPowerSave = 0;
            u8_LinYunPowerParamGapCount = 0;
        }
    }
}

static void StartRefIonGeneratorWork(void)
{
    u16_RefIonTargetCycles = U16_APP_ION_ENABLE_TOTAL_CYCLES;
    u16_RefIonCycles = 0;
    u16_RefIonCount = 0;
}

static void StopRefIonGeneratorWork(void)
{
    u16_RefIonTargetCycles = 0;
    u16_RefIonCycles = 0;
    u16_RefIonCount = 0;
}

static void StartFrzIonGeneratorWork(void)
{
    u16_FrzIonTargetCycles = U16_APP_ION_ENABLE_TOTAL_CYCLES;
    u16_FrzIonCycles = 0;
    u16_FrzIonCount = 0;
}

static void StopFrzIonGeneratorWork(void)
{
    u16_FrzIonTargetCycles = 0;
    u16_FrzIonCycles = 0;
    u16_FrzIonCount = 0;
}

static void JudgeRefFrzIonState(void)
{
    if(u16_RefIonCycles < u16_RefIonTargetCycles && 
       Get_ResolvedDeviceStatusFSM(FSM_OpenDoorControl, DEVICE_IonGenerator) == DS_DontCare)
    {
        if(u16_RefIonCount < U16_REF_ION_CYCLE_ONSECOND)
        {
            Vote_DeviceStatus_Immediate(FSM_AppControl, DEVICE_IonGenerator, DS_On);
        }
        else
        {
            Vote_DeviceStatus_Immediate(FSM_AppControl, DEVICE_IonGenerator, DS_Off);
        }

        if(u16_RefIonCount == (U16_REF_ION_CYCLE_ONSECOND + U16_REF_ION_CYCLE_OFFSECOND))
        {
            u16_RefIonCycles++;
            u16_RefIonCount = 0;
        }
        else
        {
            u16_RefIonCount++;
        }
    }
    else
    {
        Vote_DeviceStatus_Immediate(FSM_AppControl, DEVICE_IonGenerator, DS_DontCare);
        if(u16_RefIonCycles >= u16_RefIonTargetCycles && u8_RefIonEnable > 0)
        {
            StopRefIonGeneratorWork();     
            u8_RefIonEnable = 0;   
        }
    }

    if(u16_FrzIonCycles < u16_FrzIonTargetCycles && 
       Get_ResolvedDeviceStatusFSM(FSM_OpenDoorControl, DEVICE_FrzIonGenerator) == DS_DontCare)
    {
        if(u16_FrzIonCount < U16_FRZ_ION_CYCLE_ONSECOND)
        {
            Vote_DeviceStatus_Immediate(FSM_AppControl, DEVICE_FrzIonGenerator, DS_On);
        }
        else
        {
            Vote_DeviceStatus_Immediate(FSM_AppControl, DEVICE_FrzIonGenerator, DS_Off);
        }

        if(u16_FrzIonCount == (U16_FRZ_ION_CYCLE_ONSECOND + U16_FRZ_ION_CYCLE_OFFSECOND))
        {
            u16_FrzIonCycles++;
            u16_FrzIonCount = 0;
        }
        else
        {
            u16_FrzIonCount++;
        }
    }
    else
    {
        Vote_DeviceStatus_Immediate(FSM_AppControl, DEVICE_FrzIonGenerator, DS_DontCare);
        if(u16_FrzIonCycles >= u16_FrzIonTargetCycles && u8_FrzIonEnable > 0)
        {
            StopFrzIonGeneratorWork();     
            u8_FrzIonEnable = 0;   
        }
    }
}

static void JudgeMuteState(void)
{
    CoolingCapacityState_t cool_state = Get_CoolingCapacityState();
    CoolingCompState_t comp_state = Get_CoolingCompState();
    CoolingOutputState_t output_cool_state = Get_CoolingOutputState();
    FridgeState_t device_state = Get_FridgeState();
    RunningState_t run_state = Get_RunningState();
    uint16_t compon_mintues = Get_CompStillOnTimeMinute();
    bool advance_freq = false;
    uint8_t icemaker_mode;
    uint8_t value = 0;
    uint8_t mode;

    GetSysParam(SYSPARAM_LINGYUN_MUTE, &value);
    GetSysParam(SYSPARAM_USER_MODE, &mode);
    GetSysParam(SYSPARAM_ICEMAKER_FUNC, &icemaker_mode);

    if(value == 0 ||
       device_state != eFridge_Running ||
       compon_mintues > MUTE_COMPON_MAX_MINUTES ||
       mode == eDeepFreeze_Mode ||
       mode == eTurboFreeze_Mode ||
       icemaker_mode == eIceMaker_Quick_Mode
    )
    {
        mute_mode = LINYUN_MUTE_LOCAL;
        return;
    }

    if(run_state == eRunning_CoolingCycle &&
       comp_state == eCooling_CompOn &&
       (cool_state != eCoolingCapacity_Normal && cool_state != eCoolingCapacity_HighLoad))
    {
        mute_mode = LINYUN_MUTE_LOCAL;
        return;
    }

    if(mute_mode == LINYUN_MUTE_LOCAL)
    {
        if(run_state == eRunning_CoolingCycle &&
           comp_state == eCooling_CompOn &&
           cool_state == eCoolingCapacity_Normal)
        {
            u16_LastCompCoolingDuration = compon_mintues;
            return;
        }
        else if(run_state == eRunning_CoolingCycle &&
                comp_state == eCooling_CompOff &&
                cool_state == eCoolingCapacity_Normal)
        {
            if(u16_LastCompCoolingDuration > 0 &&
               u16_LastCompCoolingDuration < MUTE_NORMAL_HIGHLOAD_MINUTES)
            {
                mute_mode = LINYUN_MUTE_NORMAL;
                u16_MuteNormalHighLoadStart = Get_MinuteCount();   
            }
        }
        else
        {
            u16_LastCompCoolingDuration = 0;
        }
    }

    if(cool_state != output_cool_state)
    {
        advance_freq = true;
    }

    if(run_state == eRunning_CoolingCycle &&
       comp_state == eCooling_CompOn &&
       advance_freq == true &&
       mute_mode == LINYUN_MUTE_NORMAL)
    {
        if(Get_MinuteElapsedTime(u16_MuteNormalHighLoadStart) > MUTE_NORMAL_HIGHLOAD_MINUTES)
        {
            mute_mode = LINYUN_MUTE_LOCAL;
        }
    }
    else if(mute_mode == LINYUN_MUTE_NORMAL)
    {
        u16_MuteNormalHighLoadStart = Get_MinuteCount();
    }

    if(run_state == eRunning_CoolingCycle &&
       comp_state == eCooling_CompOn &&
       advance_freq == true &&
       mute_mode == LINYUN_MUTE_DEEP)
    {
        if(Get_MinuteElapsedTime(u16_MuteDeepHighLoadStart) > MUTE_NORMAL_HIGHLOAD_MINUTES)
        {
            mute_mode = LINYUN_MUTE_NORMAL;
            u16_MuteNormalHighLoadStart = Get_MinuteCount();
        }
    }
    else if(mute_mode == LINYUN_MUTE_DEEP)
    {
        u16_MuteDeepHighLoadStart = Get_MinuteCount();
    }

    if(mute_mode == LINYUN_MUTE_DEEP)
    {
        if(Get_MinuteElapsedTime(u16_MuteDeepStart) > MUTE_DEEP_MINUTES)
        {
            mute_mode = LINYUN_MUTE_NORMAL;
            u16_MuteNormalHighLoadStart = Get_MinuteCount();
        }
    }
    else
    {
        u16_MuteDeepStart = Get_MinuteCount();
    }

}

static uint8_t JudgeDeepMuteFrzSet(void)
{
    uint8_t frz_set;

    if(frz_set >= FRZ_LEVEL_F17)
    {
        return FRZ_LEVEL_F16;
    }

    return frz_set + 2;
}

void Clean_PeekValleyFrzSet(void)
{
    b_PeekValleyPowerFrz = false;
}

void Clean_PeekValleyRefSet(void)
{
    b_PeekValleyPowerRef = false;
}

void Set_PeekValleyFrzSet(uint8_t u8_SetTemp)
{
    if(u8_SetTemp < FRZ_LEVEL_F24 || u8_SetTemp > FRZ_LEVEL_F16)
    {
        return;
    }

    u8_PeekValleyPowerFrzSet = u8_SetTemp;
    b_PeekValleyPowerFrz = true;
    u16_PeekValleyPowerTimerStart = Get_MinuteCount();
    ActiveCloudNetState();
}

void Set_PeekValleyRefSet(uint8_t u8_SetTemp)
{
    if(u8_SetTemp < REF_LEVEL_2 || u8_SetTemp > REF_LEVEL_8)
    {
        return;
    }

    u8_PeekValleyPowerRefSet = u8_SetTemp;
    b_PeekValleyPowerRef = true;
    u16_PeekValleyPowerTimerStart = Get_MinuteCount();
    ActiveCloudNetState();
}

uint8_t Get_LinYunPowerSave(void)
{
    return u8_LinYunPowerSave;
}

void Disable_LinYunPowerSave(void)
{
    u8_LinYunPowerSave = 0;
    u8_LinYunPowerParamGapCount = 0;
}

linyun_power_param_st *Get_LinYunPowerParam(void)
{
    return &lyparam;
}

int Set_LinYunPowerParam(char *paramstrs)
{
    char *endptr;
    char *token = NULL;
    uint8_t pcount = 0;
    char delim[] = ",\"";
    linyun_power_param_st param;

    if(paramstrs[0] != '\"')
    {
        return -1;
    }
    paramstrs += 1;
    memset(&param, 0, sizeof(linyun_power_param_st));
    token = strtok(paramstrs, delim);
    while(NULL != token && pcount < LINYUN_POWER_PARAM_COUNT)
    {
        long num = strtol(token, &endptr, 10);
        if(*endptr != '\0')
        {
            return -1;
        }

        switch(pcount)
        {
        case LINYUN_POWER_PARAM_STATE:
            param.state = num;
            break;   
        case LINYUN_POWER_PARAM_COMPFREQ:
            param.freq = num;
            break; 
        case LINYUN_POWER_PARAM_VALVE:
            param.valve = num;
            break;
        case LINYUN_POWER_PARAM_FRZFAN:
            param.frzfan = num;
            break;
        case LINYUN_POWER_PARAM_REFFAN:
            param.reffan = num;
            break;
        case LINYUN_POWER_PARAM_COOLFAN:
            param.coolfan = num;
            break;
        case LINYUN_POWER_PARAM_VARDAMPER:
            param.vardamper = num;
            break;
        case LINYUN_POWER_PARAM_REFDAMPER:
            param.refdamper = num;
            break;     
        case LINYUN_POWER_PARAM_REFDAMPER1:
            param.refdamper1 = num;
            break; 
        }
        token = strtok(NULL, delim);
        pcount++;
    }

    if(pcount < LINYUN_POWER_PARAM_COUNT)
    {
        return -1;
    }

    memcpy(&lyparam, &param, sizeof(linyun_power_param_st));
    if(lyparam.state < 0)
    {
        u8_LinYunPowerParamReport = 0;
    }
    else
    {
        u8_LinYunPowerParamReport = 1;
    }

    if(u8_LinYunPowerParamGap == 0)
    {
        u16_LinYunPowerTimerStart = Get_MinuteCount();
        u8_LinYunPowerParamGap = 1;
        u8_LinYunPowerParamGapCount = 0;
    }
    else
    {
        if(u8_LinYunPowerSave)
        {
            if(Get_MinuteElapsedTime(u16_LinYunPowerTimerStart) > LINYUN_POWERPARAM_GAP_MINUTES)
            {
                if(u8_LinYunPowerParamGapCount < U8_MAX_UINT8)
                {
                    u8_LinYunPowerParamGapCount++;
                }
            }
            else
            {
                u8_LinYunPowerParamGapCount = 0;
            }
            u16_LinYunPowerTimerStart = Get_MinuteCount();
        }
        else
        {
            if(Get_MinuteElapsedTime(u16_LinYunPowerTimerStart) <= LINYUN_POWERPARAM_GAP_MINUTES)
            {
                if(u8_LinYunPowerParamGapCount < U8_MAX_UINT8)
                {
                    u8_LinYunPowerParamGapCount++;
                }
            }
            else
            {
                u8_LinYunPowerParamGapCount = 0;
            }
            u16_LinYunPowerTimerStart = Get_MinuteCount();
        }
    }
    return 0;
}

void Set_RefIonEnable(uint8_t enable)
{
    u8_RefIonEnable = enable;
    if(enable)
    {
        StartRefIonGeneratorWork();
    }
    else
    {
        StopRefIonGeneratorWork();
        StopAutoRefIonGeneratorWork();
    }
}

uint8_t Get_RefIonEnable(void)
{
    return (u8_RefIonEnable || IsRefIonGeneratorWorking());
}

void Set_FrzIonEnable(uint8_t enable)
{
    u8_FrzIonEnable = enable;
    if(enable)
    {
        StartFrzIonGeneratorWork();
    }
    else
    {
        StopFrzIonGeneratorWork();
        StopAutoFrzIonGeneratorWork();
    }
}

uint8_t Get_FrzIonEnable(void)
{
    return (u8_FrzIonEnable || IsFrzIonGeneratorWorking());
}

void CloudControlFunc(void)
{
    int16_t pv_frzset;
    int16_t pv_refset;
    uint8_t linyun_mute; 

    JudgeCloudNetState();
    JudgeMuteState();
    linyun_mute = GetSysParam(SYSPARAM_LINGYUN_MUTE, &linyun_mute);
    peekValleyPowerFrzTemp = JudgePeekValleyPowerTempFrzSet();
    peekValleyPowerRefTemp = JudgePeekValleyPowerTempRefSet();
    pv_frzset = peekValleyPowerFrzTemp;
    pv_refset = peekValleyPowerRefTemp;

    if(mute_mode == LINYUN_MUTE_DEEP && linyun_mute > 0)
    {
        Update_FrzSetTempNotSave(JudgeDeepMuteFrzSet());
    }
    else if(pv_frzset > 0)
    {
        Update_FrzSetTempNotSave(pv_frzset);
    }
    else
    {
        Update_UserModeTempFrzSet();
    }

    if(pv_refset > 0)
    {
        Update_RefSetTempNotSave(pv_refset);
    }
    else
    {
        Update_UserModeTempRefSet();
    }

    JudgeRefFrzIonState();
    JudgeLinYunPowerSave();
    UploadLinYunPowerSave();
    JudgeSmartGridState();
}

uint8_t GetPeekValleyPowerFrzTemp(void)
{
    if(peekValleyPowerFrzTemp > 0)
    {
        return peekValleyPowerFrzTemp;
    }
    return 0;
}

uint8_t GetPeekValleyPowerRefTemp(void)
{
    if(peekValleyPowerRefTemp > 0)
    {
        return peekValleyPowerRefTemp;
    }
    return 0;
}

int UploadLinYunPowerParam(char *pResult)
{
    int16_t val;
    uint16_t len = 0;
    bool b_condensation = Get_CondensationModeState();

    len += snprintf(pResult, CMD_RESULT_BUF_SIZE, "properties_changed 13 7 \"");
    val = Get_SensorValue(SENSOR_ROOM) - 500;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_HumidityRange();
    val = val * 5 + 10;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_SensorValue(SENSOR_REF) - 500;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_SensorValue(SENSOR_REF_DEFROST) - 500;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_SensorValue(SENSOR_FRZ) - 500;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_SensorValue(SENSOR_DEFROST) - 500;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_SensorValue(SENSOR_ICEMAKER_BOTTOM) - 500;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_SensorValue(SENSOR_ICEMAKER_TOP) - 500;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_SensorValue(SENSOR_ICEMAKER_BOTTOMX) - 500;
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_RefSetTemp() - 1;
    if(b_condensation && val < 8)
    {
        val += 1;
    }
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_FrzSetTemp() - 30;
    if(b_condensation && val < -16)
    {
        val += 1;
    }
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_RefVarSetTemp();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_DoorOpenTimeSecond(DOOR_REF_LEFT);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_DoorOpenTimeSecond(DOOR_REF_RIGHT);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_DoorOpenTimeSecond(DOOR_REF);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_DoorOpenTimeSecond(DOOR_FRZ_LEFT);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_DoorOpenTimeSecond(DOOR_FRZ_RIGHT);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_DoorOpenTimeSecond(DOOR_FRZ);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_CompFeedbackFreq();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_CompPower();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_CompFreq();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_ValveState();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_VarDamperState();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_RefDamperState();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", 0);
    val = Get_FanDuty(FRZ_FAN);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_FanDuty(COOL_FAN);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_FanDuty(REF_FAN);
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_DefrostMode();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_DefrostingtimeSecond();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d,", val);
    val = Get_RefFrostReduceMode();
    len += snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "%d", val);
    snprintf(pResult + len, CMD_RESULT_BUF_SIZE - len, "\"\r");
    return 0;
}

void Set_Strong_Cool(uint8_t enable)
{
    u8_Strong_Cool = enable;
}

bool Get_Strong_Cool(void)
{
    return (u8_Strong_Cool > 0 ? true : false);
}

void Set_Strong_Mute(uint8_t enable)
{
    u8_Strong_Mute = enable;
}

bool Get_Strong_Mute(void)
{
    return (u8_Strong_Mute > 0 ? true : false);
}

void Set_Mute_Mode(uint8_t mode)
{
    CoolingCapacityState_t cool_state = Get_CoolingCapacityState();
    FridgeState_t device_state = Get_FridgeState();
    RunningState_t run_state = Get_RunningState();

    ActiveCloudNetState();
    if(mode == LINYUN_MUTE_LOCAL)
    {
        mute_mode = LINYUN_MUTE_LOCAL;
        return;
    }
    else if(mode >= LINYUN_MUTE_MAX || 
       device_state != eFridge_Running ||
       run_state != eRunning_CoolingCycle)
    {
        return;
    }

    if(mode != mute_mode)
    {
        if(mode == LINYUN_MUTE_NORMAL || mode == LINYUN_MUTE_DEEP )
        {
            if(mode == LINYUN_MUTE_NORMAL)
            {
                u16_MuteNormalHighLoadStart = Get_MinuteCount();
            }
            else if(mode == LINYUN_MUTE_DEEP)
            {
                u16_MuteDeepHighLoadStart = Get_MinuteCount();
                u16_MuteDeepStart = Get_MinuteCount();
            }
        }
        mute_mode = mode;
    }
    return;
}

uint8_t Get_Mute_Mode(void)
{
    return mute_mode;
}

uint8_t Get_SmartGrid_Deforst(void)
{
    return u8_SmartGrid_Deforst;
}

void Set_SmartGrid_Deforst(uint8_t enable)
{
    u8_SmartGrid_Deforst = enable;
    ActiveCloudNetState();
    JudgeSmartGridState();
}

uint8_t Get_SmartGrid_Forbid_Deforst(void)
{
    return u8_SmartGrid_Forbid_Deforst;
}

uint8_t Get_SmartGrid_Forbid_LoadDeforst(void)
{
    if(u8_SmartGrid_Forbid_DelayLoadDeforst == 0 &&
       u8_SmartGrid_Forbid_ReduceLoadDeforst == 0)

    {
        return 0;
    }
    return 1;
}

void Set_SmartGrid_DelayLoad(uint8_t enable)
{
    u8_SmartGrid_DelayLoad = enable;
    if(enable)
    {
        u16_SmartGridDelayLoadStart = Get_MinuteCount();
    }
    return;
}

uint8_t Get_SmartGrid_DelayLoad(void)
{
    return u8_SmartGrid_DelayLoad;
}

void Set_SmartGrid_ReduceLoad(uint8_t enable)
{
    u8_SmartGrid_ReduceLoad = enable;
    if(enable)
    {
        u16_SmartGridReduceLoadStart = Get_MinuteCount();
    }
    return;
}

uint8_t Get_SmartGrid_ReduceLoad(void)
{
    return u8_SmartGrid_ReduceLoad;
}

uint8_t Is_SmartGrid_DelayLoadOn(void)
{
    return u8_SmartGrid_DelayLoad_On;
}

uint8_t Is_SmartGrid_ReduceLoadOn(void)
{
    return u8_SmartGrid_ReduceLoad_On;
}
