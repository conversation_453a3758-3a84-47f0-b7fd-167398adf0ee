/*!
 * @file
 * @brief This file defines public constants, types and functions for the cooling controller.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef COOLING_CYCLE_H
#define COOLING_CYCLE_H

#include <stdint.h>
#include <stdbool.h>
#include "SimpleFsm.h"
#include "Driver_Fan.h"
#include "Parameter_Device.h"

#define COOLING_PROTECT_TIME_SECONDS (uint16_t)420 // 7min
#define VLAVE_DELAY_STARTUP_SECOND (uint16_t)20
#define VLAVE_RESET_PROTECT_SECOND (uint16_t)10
#define FAN_DELAY_STARTUP_SECOND (uint16_t)80
#define DAMPER_DELAY_STARTUP_SECOND (uint16_t)90
#define COOL_FAN_DELAY_ON_SECOND (uint16_t)300 // 5min
#define CONDENSATION_COOL_FAN_DELAY_SECOND (uint16_t)480 // 8min
#define CONDENSATION_COOL_FAN_ON_SECOND (uint16_t)300 // 5min
#define CONDENSATION_COOL_FAN_OFF_SECOND (uint16_t)180 // 3min
#define CONDENSATION_COOL_FAN_DUTY (uint8_t)35
#define VALVE_SWITCH_REF_FAN_DUTY (uint8_t)50
#define COMP_ON_CONTINUE_SECONDS (uint16_t)(60 * 60)
#define DEFORST_COMP_ON_CONTINUE_SECONDS (uint16_t)(2 * 60 * 60)
#define COMP_ON_CONTINUE_REF_COOL_MINUTES (uint16_t)(2)
#define U16_REF_VLAVE_CHECK_TIME_MINUTES ((uint16_t)(10))
#define U16_FRZ_VLAVE_CHECK_TIME_MINUTES ((uint16_t)(10))

enum
{
    eCooling_CompOff = 0,
    eCooling_CompProtect,
    eCooling_CompStartup,
    eCooling_CompOn
};
typedef uint8_t CoolingCompState_t;

enum
{
    eMode_FridgePowerOn = 0,
    eMode_FactoryCompleted,
    eMode_DefrostingCompleted
};
typedef uint8_t CoolingEntryMode_t;

typedef struct
{
    uint16_t u16_CoolingCycleNumber;
    uint16_t u16_CompTotalOnTimeSuspend;
    uint16_t u16_CompTotalOnTimeMinute;
    uint16_t u16_CompStillOnTimeStart;
    uint16_t u16_CompStillOnTimeMinute;
    CoolingEntryMode_t coolingEntryMode;
    bool b_PullDown;
    bool b_DeepFreeze;
    bool b_TurboFreeze;
    bool b_TurboCool;
    bool b_HighLoad;
    bool b_ValveReset;
    bool b_ValveRestAction;
    bool b_CompOnStillRefCool;
    bool b_CompOnFirstRefCool;
    bool b_PowerOnPullDownState;
} Cooling_st;

void CoolingCycle_Init(CoolingEntryMode_t mode);
void CoolingCycle_Exit(void);
CoolingCompState_t Get_CoolingCompState(void);
CoolingCapacityState_t Get_CoolingCapacityState(void);
void Clear_CompStillOnTimeMinute(void);
void Clear_CompTotalOnTimeMinute(void);
uint16_t Get_CompStillOnTimeMinute(void);
uint16_t Get_CompTotalOnTimeMinute(void);
uint16_t Get_CompOffTimeSecond(void);
void Set_TurboCoolState(bool state);
void Set_DeepFreezeState(bool state);
void Set_TurboFreezeState(bool state);
bool Get_TurboCoolState(void);
bool Get_TurboFreezeState(void);
bool Get_DeepFreezeState(void);
CoolingEntryMode_t Get_CoolingEntryMode(void);
uint16_t Get_CoolingCycleNumber(void);
bool Get_PowerOnPullDownState(void);
#endif
