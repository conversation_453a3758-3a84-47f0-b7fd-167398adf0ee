/*!
 * @file
 * @brief This file defines public constants, types and functions for the fan drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __SBUS_ICEMAKER_H
#define __SBUS_ICEMAKER_H

#include "Sbus_Core.h"

#define ICEMAKER_EVENT_POLL_TIMER_MS 500
#define ICEMAKER_ERROR_EVENT_POLL_TIMER_MS 3000
#define ICEMAKER_WATER_TIMER_MS 60000
#define ICEMAKER_BOOT_MS 2000
#define ICEMAKER_MTYPE_SYNC_MINUTES 30

typedef struct
{
    uint8_t init;
    uint8_t icemaker_mode;
    uint8_t icemaker_state;
    uint32_t icemaker_reserve;
    uint8_t icemaker_volume;
    uint16_t temp_bottomx;
    uint8_t ledctrl;
    uint16_t temp_bottom;
    uint16_t temp_top;
    uint16_t frz_temp;
    int8_t frz_set_temp;
    uint8_t mode;
    uint8_t room_temp;
    uint8_t reserve_state;
    uint8_t clean_state;
    uint8_t icemaker_test;
    uint8_t icemaker_load;
    uint8_t icemaker_fault;
    uint8_t icemaker_force;
    uint8_t deforst_state;
    int8_t  deforst_sensor;
    uint8_t icemaker_doing;
    uint16_t icemaker_target;
    uint16_t icemaker_history;
    uint16_t icemaker_current;
    uint16_t icemaker_waterlog;
    uint16_t force_icemaker_time;
    uint8_t force_wheater_time;
    uint8_t wheater_time;
    uint8_t force_bheater_time;
    uint8_t bheater_time;
    uint16_t force_isensor_onoff_val;
    uint16_t isensor_val;
    uint16_t force_waterlog_time;
    uint16_t door_state;
    uint8_t cooling_state;
    uint8_t system_state;
    uint8_t deforst_mode;
    uint32_t error;
    uint32_t pfault;
    uint16_t short_time;
    uint8_t mtype;
} icemaker_param_st;

typedef enum
{
    ICEMAKER_PROPERTY_TYPE_MACHINE = 0,
    ICEMAKER_PROPERTY_TYPE_TEMP_BOTTOM,
    ICEMAKER_PROPERTY_TYPE_TEMP_TOP,
    ICEMAKER_PROPERTY_TYPE_LEDCTRL,
    ICEMAKER_PROPERTY_TYPE_TEMP_BOTTOMX,
    ICEMAKER_PROPERTY_TYPE_FRZ_TEMP,
    ICEMAKER_PROPERTY_TYPE_ROOM_TEMP,
    ICEMAKER_PROPERTY_TYPE_USR_MODE,
    ICEMAKER_PROPERTY_TYPE_FRZ_SET,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_TEST,
    ICEMAKER_PROPERTY_TYPE_CLEAN_STATE,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_FORCE_LOAD,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_FAULT,
    ICEMAKER_PROPERTY_TYPE_FORCE_ICEMAKER,
    ICEMAKER_PROPERTY_TYPE_FORCE_WHEATER,
    ICEMAKER_PROPERTY_TYPE_WHEATER,
    ICEMAKER_PROPERTY_TYPE_FORCE_WATERLOG,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_WATERLOG,
    ICEMAKER_PROPERTY_TYPE_FORCE_BHEATER,
    ICEMAKER_PROPERTY_TYPE_BHEATER,
    ICEMAKER_PROPERTY_TYPE_FORCE_ISENSOR_ONOFF,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_SENSOR,
    ICEMAKER_PROPERTY_TYPE_DEFORST_STATE,
    ICEMAKER_PROPERTY_TYPE_DEFORST_SENSOR,
    ICEMAKER_PROPERTY_TYPE_SYSTEM_STATE,
    ICEMAKER_PROPERTY_TYPE_DEFORST_MODE,
    ICEMAKER_PROPERTY_TYPE_COOLING_STATE,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_LOAD_STATE,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_DOING,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_TARGET,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_HISTORY,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_CURRENT,
    ICEMAKER_PROPERTY_TYPE_FAULT,
    ICEMAKER_PROPERTY_TYPE_PASSIVE_FAULT,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_MODE,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_STATE,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_RESERVE,
    ICEMAKER_PROPERTY_TYPE_ICEMAKER_VOLUME,
    ICEMAKER_PROPERTY_TYPE_RESERVE_STATE,
    ICEMAKER_PROPERTY_TYPE_DOOR_STATE,
    ICEMAKER_PROPERTY_TYPE_SHORT_TIME,
    ICEMAKER_PROPERTY_TYPE_INIT,
    ICEMAKER_PROPERTY_TYPE_MAX
} icemaker_property_type_e;

typedef struct
{
    sbus_slave_st slave;
    fireware_frame_st frame;
    uint16_t poll_count;
    uint16_t boot_count;
    uint16_t hwversion;
    uint16_t appversion;
    uint16_t bootVersion;
    uint16_t bootCrc;
    uint16_t appcrc;
    bool b_appversion;
    bool b_bootversion;
    bool dirty;
    bool response;
    bool init;
    bool alldirty;
    uint32_t water_timer;
    uint16_t mtype_timer;
} sbus_icemaker_st;

enum
{
    eIceMaker_Normal_Mode = 0,
    eIceMaker_Quick_Mode,
    eIceMaker_Clean_Mode,
    eIceMaker_Stop_Mode,
    eIceMaker_Max_Mode
};
typedef uint8_t IceMakerMode_t;

void Init_SbusIceMaker(void);
bool GetIceMakerWorkErr(void);
bool GetIceMakerDownSensorErr(void);
bool GetIceMakerUpSensorErr(void);
bool GetIceMakerDownBehindSensorErr(void);
bool Get_IceMakerCommErr(void);
void Handle_IceMaker_Overtime(void);
int8_t GetIceMakerPropertyValue(icemaker_property_type_e type, void *data);
int8_t SetIceMakerPropertyValue(icemaker_property_type_e type, void *data);
bool GetIceMakerPropertyState(icemaker_property_type_e type);
uint32_t Get_IceMakerBootVersion(void);
uint32_t Get_IceMakerBootCrc(void);
uint32_t Get_IceMakerAppVersion(void);
uint32_t Get_IceMakerAppCrc(void);
void Ctrl_LeftVarLed(bool enable);
void Ctrl_RightVarLed(bool enable);
sbus_slave_stats *Get_IceMakerPacketStats(void);
#endif
