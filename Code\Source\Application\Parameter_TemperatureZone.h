/*!
 * @file
 * @brief This file defines public constants, types and functions for the zone parameter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef PARAMETER_TEMPRATURE_ZONE_H
#define PARAMETER_TEMPRATURE_ZONE_H

#include <stdint.h>
#include <stdbool.h>
#include "Driver_AdSample.h"

#define U8_REF_LEVEL_LENGTH ((uint8_t)7)
#define U8_REF_LEVEL_MIN ((uint8_t)REF_LEVEL_2)
#define U8_REF_LEVEL_MAX ((uint8_t)REF_LEVEL_8)
#define U16_REF_ON_OFF_MIN_DIFFERENCE_VALUE ((uint16_t)(1 * 10))
#define U16_REF_ON_PLUS_TEMP_OFFSET (uint16_t)5
#define U16_ENERGY_MODE_REF_ON_PLUS_TEMP_OFFSET (uint16_t)30
#define U16_PULL_DOWN_FRZ_TEMP CON_0P0_DEGREE
#define U16_PULL_DOWN_REF_TEMP CON_0P0_DEGREE
#define U16_PULL_DOWN_REFVAR_TEMP CON_0P0_DEGREE
#define U16_HIGH_LOAD_FRZ_TEMP CON_0P0_DEGREE
#define U16_FRZ_ON_TEMP CON_F15P0_DEGREE
#define U16_FRZ_OFF_TEMP CON_F16P0_DEGREE
#define HIGH_LOAD_REF_SNR_ON_PLUS_TEMP_OFFSET (uint16_t)20
#define HIGH_LOAD_FRZ_SNR_ON_PLUS_TEMP_OFFSET (uint16_t)30
#define HIGH_LOAD_VAR_SNR_ON_PLUS_TEMP_OFFSET (uint16_t)30
#define REF_SNR_FAN_ADVANCE_TEMP_OFFSET (uint16_t)10
#define FRZ_SNR_FAN_ADVANCE_TEMP_OFFSET (uint16_t)20
#define VAR_SNR_FAN_ADVANCE_TEMP_OFFSET (uint16_t)20

#define U16_AFTER_DEFROST_REF_SNR_ON_TEMP_OFFSET (uint16_t)20
#define U16_AFTER_DEFROST_REF_SNR_OFF_TEMP_OFFSET (uint16_t)20

#define U16_ENERGY_MODE_REF_SNR_ON_TEMP_OFFSET (uint16_t)0
#define U16_ENERGY_MODE_REF_SNR_OFF_TEMP_OFFSET (uint16_t)15

#define U16_ENERGY_MODE_FRZ_SNR_ON_TEMP_OFFSET (uint16_t)10

#define U16_ENERGY_MODE_REF_VAR_SNR_ON_TEMP_OFFSET (uint16_t)20
#define U16_ENERGY_MODE_REF_VAR_SNR_OFF_TEMP_OFFSET (uint16_t)20

#define U16_CONDENSATION_MODE_REF_SNR_ON_TEMP_OFFSET (uint16_t)5
#define U16_CONDENSATION_MODE_REF_SNR_OFF_TEMP_OFFSET (uint16_t)5

#define U16_CONDENSATION_MODE_REFVAR_SNR_ON_TEMP_OFFSET (uint16_t)10
#define U16_CONDENSATION_MODE_REFVAR_SNR_OFF_TEMP_OFFSET (uint16_t)10

#define U16_CONDENSATION_MODE_FRZ_SNR_ON_TEMP_OFFSET (uint16_t)10
#define U16_CONDENSATION_MODE_FRZ_SNR_OFF_TEMP_OFFSET (uint16_t)10

#define U16_OVERLOAD_DEFROST_REF_ON_TEMP_OFFSET ((uint16_t)5 * 10)
#define U16_OVERLOAD_DEFROST_REF_TEMP ((uint16_t)CON_10P0_DEGREE)
#define U16_OVERLOAD_DEFROST_FRZ_TEMP ((uint16_t)CON_F10P0_DEGREE)
#define U16_OVERLOAD_DEFROST_REF_ON_TEMP_TIME_MIN ((uint16_t)6 * 60)

#define U8_FRZ_LEVEL_LENGTH ((uint8_t)9)
#define U8_FRZ_LEVEL_MIN ((uint8_t)FRZ_LEVEL_F30)
#define U8_FRZ_ON_OFFLEVEL_MIN ((uint8_t)FRZ_LEVEL_F24)
#define U8_FRZ_LEVEL_MAX ((uint8_t)FRZ_LEVEL_F16)

#define PRE_COOLING_REF_SNR_OFF_TEMP_OFFSET (uint16_t)5
#define PRE_COOLING_FRZ_SNR_OFF_TEMP_OFFSET (uint16_t)20

#define U16_ICEMAKER_QUICK_MODE_ONOFF_OFFSET (uint16_t)20

#define REFVAR_LEVEL_OFFSET eRefVar_FreshMeat

enum
{
    eRefVar_Treasure = 0,
    eRefVar_Baby,
    eRefVar_Zero,
    eRefVar_FreshMeat,
    eRefVar_Aquatic,
    eRefVar_Thaw,
    eRefVar_Smoothie,
    eRefVar_Ref,
    eRefVar_Max
};
typedef uint8_t RefVarSet_t;

typedef enum
{
    REF_LEVEL_OFF = 0, // RefClose
    REF_LEVEL_0, //  0℃
    REF_LEVEL_1, //  1℃
    REF_LEVEL_2, //  2℃
    REF_LEVEL_3, //  3℃
    REF_LEVEL_4, //  4℃
    REF_LEVEL_5, //  5℃
    REF_LEVEL_6, //  6℃
    REF_LEVEL_7, //  7℃
    REF_LEVEL_8 //  8℃
} RefLevel_em;

typedef enum
{
    FRZ_LEVEL_F30 = 0, // -30℃
    FRZ_LEVEL_F29, // -29℃
    FRZ_LEVEL_F28, // -28℃
    FRZ_LEVEL_F27, // -27℃
    FRZ_LEVEL_F26, // -26℃
    FRZ_LEVEL_F25, // -25℃
    FRZ_LEVEL_F24, // -24℃
    FRZ_LEVEL_F23, // -23℃
    FRZ_LEVEL_F22, // -22℃
    FRZ_LEVEL_F21, // -21℃
    FRZ_LEVEL_F20, // -20℃
    FRZ_LEVEL_F19, // -19℃
    FRZ_LEVEL_F18, // -18℃
    FRZ_LEVEL_F17, // -17℃
    FRZ_LEVEL_F16, // -16℃
} FrzLevel_em;

typedef struct
{
    uint16_t u16_OnTemp;
    uint16_t u16_OffTemp;
} ZoneOnOffTemp_st;

typedef struct
{
    uint16_t u16_OnTime;
    uint16_t u16_OffTime;
} ZoneOnOffTime_st;

enum
{
    eZoneCooling_Idle = 0,
    eZoneCooling_Ref,
    eZoneCooling_Frz,
    eZoneCooling_RefFrz,
};
typedef uint8_t ZoneCoolingState_t;

typedef struct
{
    uint16_t u16_RefOverLoadTimeMinute;
    uint16_t u16_RefSnrTemp;
    uint16_t u16_RefSnrErrTime;
    uint16_t u16_RefOnPlusTemp;
    uint16_t u16_RefOnTemp;
    uint16_t u16_RefOffTemp;
    uint16_t u16_RefOnTime;
    uint16_t u16_RefOffTime;
    uint16_t u16_RefVarSnrTemp;
    uint16_t u16_RefVarOnTemp;
    uint16_t u16_RefVarOffTemp;
    uint16_t u16_VarSnrErrTime;
    uint16_t u16_VarOnTime;
    uint16_t u16_VarOffTime;
    uint16_t u16_FrzSnrTemp;
    uint16_t u16_FrzSnrErrTime;
    uint16_t u16_FrzOnTemp;
    uint16_t u16_FrzOffTemp;
    uint16_t u16_FrzOnTime;
    uint16_t u16_FrzOffTime;
    ZoneCoolingState_t zoneCoolingState;
    ZoneCoolingState_t zonePreCoolingState;
    int8_t s8_RefOnInchValue;
    int8_t s8_RefOffInchValue;
    int8_t s8_RefVarOnInchValue;
    int8_t s8_RefVarOffInchValue;
    int8_t s8_FrzOnInchValue;
    int8_t s8_FrzOffInchValue;
    bool b_ComOnFrzReachedOffTempOnce;
    bool b_RefCooling;
    bool b_RefVarCoolingState;
    bool b_RefSnrError;
    bool b_RefVarSnrError;
    bool b_FrzSnrError;
} Zone_st;

void Update_TempParameter(void);
void Update_RefSetTemp(uint8_t u8_SetTemp);
void Update_RefVarSetTemp(uint8_t u8_SetTemp);
void Update_FrzSetTemp(uint8_t u8_SetTemp);
uint8_t Get_RefSetTemp(void);
uint8_t Get_RefVarSetTemp(void);
uint8_t Get_FrzSetTemp(void);
bool CoolingCycle_GetRefVarCoolingState(void);
ZoneCoolingState_t CompOff_GetZoneCoolingState(void);
ZoneCoolingState_t CompOn_GetZoneCoolingState(void);
ZoneCoolingState_t PreCooling_GetFirstCoolingState(void);
ZoneCoolingState_t PreCooling_GetSecondCoolingState(void);
ZoneCoolingState_t Get_ZoneCoolingState(void);
bool Get_FrzFanOnState(void);
bool Get_FrzFanOffState(void);
uint16_t Get_RefZoneOnTemp(void);
uint16_t Get_RefZoneOffTemp(void);
uint16_t Get_DefPreCoolingRefZoneOffTemp(uint8_t refSetTemp);
void Set_RefOnTempMicroAdjustParm(uint16_t u16_parm);
void Set_RefOffTempMicroAdjustParm(uint16_t u16_parm);
uint16_t Get_RefOnTempMicroAdjustParm(void);
uint16_t Get_RefOffTempMicroAdjustParm(void);
uint16_t Get_RefVarZoneOnTemp(void);
uint16_t Get_RefVarZoneOffTemp(void);
void Set_RefVarOnTempMicroAdjustParm(uint16_t u16_parm);
void Set_RefVarOffTempMicroAdjustParm(uint16_t u16_parm);
uint16_t Get_RefVarOnTempMicroAdjustParm(void);
uint16_t Get_RefVarOffTempMicroAdjustParm(void);
uint16_t Get_FrzZoneOnTemp(void);
uint16_t Get_FrzZoneOffTemp(void);
void Set_FrzOnTempMicroAdjustParm(uint16_t u16_parm);
void Set_FrzOffTempMicroAdjustParm(uint16_t u16_parm);
uint16_t Get_FrzOnTempMicroAdjustParm(void);
uint16_t Get_FrzOffTempMicroAdjustParm(void);
bool Get_TempPullDownState(void);
bool Get_RefTempHighLoadState(void);
bool Get_FrzTempHighLoadState(void);
bool Get_VarTempHighLoadState(void);
bool Get_FrzTempLessZero(void);
bool Get_RefTempOverLoadDefrost(void);
bool Get_FrzTempOverLoadDefrost(void);
bool Get_RefTempOverOnDefrost(void);
void Update_FrzSetTempNotSave(uint8_t u8_SetTemp);
void Update_RefSetTempNotSave(uint8_t u8_SetTemp);
void Reset_ZoneCoolingState(void);
bool IsRefFanNeedAdvance(void);
bool IsFrzFanNeedAdvance(void);
bool IsCondFanNeedAdvance(void);
bool IsVarFanNeedAdvance(void);
bool IsLinYunTempBelowOn(void);
#endif
