/*!
 * @file
 * @brief This file defines public constants, types and functions for the temperature sensor.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef DRIVER_AD_SAMPLE_H
#define DRIVER_AD_SAMPLE_H

#include <stdbool.h>
#include "Driver_AdTemperature.h"

#define U16_ZONE_TEMP_MAX ((uint16_t)1001)
#define U16_ZONE_TEMP_MIN ((uint16_t)1)

typedef enum
{
    RT_BELOW13 = 0,
    RT_BELOW18,
    RT_BELOW23,
    RT_BELOW28,
    RT_BELOW35,
    RT_BELOW40,
    RT_UP40,
    RT_MAXSIZE
} RoomTemp_em;
typedef uint8_t RoomTempRange_t;

typedef enum
{
    HBELOW10,
    HBELOW15,
    HBELOW20,
    HBELOW25,
    H<PERSON><PERSON>OW30,
    H<PERSON>LOW35,
    <PERSON><PERSON><PERSON>OW40,
    <PERSON><PERSON><PERSON>OW45,
    <PERSON><PERSON><PERSON>OW50,
    <PERSON><PERSON><PERSON>OW55,
    <PERSON><PERSON><PERSON>OW60,
    <PERSON><PERSON><PERSON><PERSON>65,
    <PERSON><PERSON><PERSON><PERSON>70,
    <PERSON><PERSON><PERSON><PERSON>75,
    <PERSON><PERSON><PERSON><PERSON>80,
    <PERSON><PERSON><PERSON><PERSON>85,
    <PERSON><PERSON><PERSON>OW90,
    <PERSON><PERSON><PERSON>OW95,
    <PERSON>UP95,
    <PERSON>UM<PERSON><PERSON>Y_MAXSIZE
} Humidity_em;
typedef uint8_t HumidityRange_t;

typedef enum
{
    SENSOR_REF,
    SENSOR_REF_DEFROST,
    SENSOR_VV,
    SENSOR_AC,
    SENSOR_DC,
    SENSOR_HUMIDITY,
    SENSOR_ROOM,
    SENSOR_FRZ,
    SENSOR_DEFROST,
    SENSOR_TYPE_MAX,
    SENSOR_ICEMAKER_BOTTOM,
    SENSOR_ICEMAKER_TOP,
    SENSOR_ICEMAKER_BOTTOMX,
    SENSOR_ICEMAKER_MAX
} SensorType_em;
typedef uint8_t SensorType_t;

typedef struct
{
    RoomTempRange_t roomRange;
    uint16_t u16_LowerLimit;
    uint16_t u16_UpperLimit;
} CalRoomRange_st;

typedef struct
{
    HumidityRange_t humidityRange;
    uint16_t u16_LowerLimit;
    uint16_t u16_UpperLimit;
} CalHumidityRange_st;

typedef struct
{
    uint32_t u32_SumAd;
    uint16_t u16_MaxAd;
    uint16_t u16_MinAd;
    uint16_t u16_FilteredAd;
    uint16_t u16_SensorValue;
    bool b_SensorError;
} AdSample_st;

void AdSample_Handle(void);
uint16_t Get_12VPower(void);
RoomTempRange_t Get_RoomTempRange(void);
HumidityRange_t Get_HumidityRange(void);
uint16_t Get_SensorValue(SensorType_t type);
bool Get_SensorError(SensorType_t type);
bool IsRoomTempBelowEightDegree(void);
#endif
